use std::fmt;
use std::sync::Arc;

/// Universal callback wrapper inspired by <PERSON><PERSON>'s Callback system.
///
/// This provides a type-safe way to pass function callbacks between components,
/// similar to React's event handlers and <PERSON><PERSON>'s callback system.
///
/// # Examples
///
/// ```rust
///
/// // Define a callback that takes a string and returns nothing
/// let on_click: Callback<String> = Callback::from(|msg: String| {
///     println!("Clicked: {}", msg);
/// });
///
/// // Emit the callback
/// on_click.emit("Hello".to_string());
///
/// // Define a callback with return value
/// let transform: Callback<i32, String> = Callback::from(|num: i32| {
///     format!("Number: {}", num)
/// });
///
/// let result = transform.emit(42);
/// assert_eq!(result, "Number: 42");
/// ```
pub struct Callback<IN, OUT = ()> {
    callback: Arc<dyn Fn(IN) -> OUT + Send + Sync>,
}

impl<IN, OUT> Clone for Callback<IN, OUT> {
    fn clone(&self) -> Self {
        Self {
            callback: self.callback.clone(),
        }
    }
}

impl<IN, OUT> Callback<IN, OUT> {
    /// Create a new callback from a function
    pub fn new<F>(func: F) -> Self
    where
        F: Fn(IN) -> OUT + Send + Sync + 'static,
    {
        Self {
            callback: Arc::new(func),
        }
    }

    /// Emit the callback with the given input value
    pub fn emit(&self, input: IN) -> OUT {
        (self.callback)(input)
    }

    /// Create a reformed callback that transforms the input before calling this callback
    ///
    /// # Examples
    ///
    /// ```rust
    /// use terminus_ui::prelude::*;
    ///
    /// let original: Callback<String> = Callback::from(|s: String| {
    ///     println!("Got: {}", s);
    /// });
    ///
    /// let reformed: Callback<i32> = original.reform(|num: i32| format!("Number: {}", num));
    /// reformed.emit(42); // Prints "Got: Number: 42"
    /// ```
    pub fn reform<F, T>(&self, func: F) -> Callback<T, OUT>
    where
        F: Fn(T) -> IN + Send + Sync + 'static,
        IN: 'static,
        OUT: 'static,
    {
        let callback = self.callback.clone();
        Callback::new(move |input: T| {
            let transformed = func(input);
            callback(transformed)
        })
    }

    /// Create a reformed callback that optionally transforms the input
    /// Only calls the original callback if the transform function returns Some
    ///
    /// # Examples
    ///
    /// ```rust
    /// use terminus_ui::prelude::*;
    ///
    /// let original: Callback<String> = Callback::from(|s: String| {
    ///     println!("Got: {}", s);
    /// });
    ///
    /// let filtered: Callback<i32, Option<()>> = original.filter_reform(|num: i32| {
    ///     if num > 0 {
    ///         Some(format!("Positive: {}", num))
    ///     } else {
    ///         None
    ///     }
    /// });
    ///
    /// filtered.emit(42);  // Prints "Got: Positive: 42", returns Some(())
    /// filtered.emit(-1);  // Does nothing, returns None
    /// ```
    pub fn filter_reform<F, T>(&self, func: F) -> Callback<T, Option<OUT>>
    where
        F: Fn(T) -> Option<IN> + Send + Sync + 'static,
        IN: 'static,
        OUT: 'static,
    {
        let callback = self.callback.clone();
        Callback::new(move |input: T| func(input).map(|v| callback(v)))
    }
}

impl<IN> Callback<IN> {
    /// Create a no-op callback that does nothing when called
    /// Useful for optional callbacks or default values
    pub fn noop() -> Self {
        Self::new(|_| {})
    }
}

impl<IN, OUT> Default for Callback<IN, OUT>
where
    OUT: Default,
{
    fn default() -> Self {
        Self::new(|_| OUT::default())
    }
}

impl<IN, OUT, F> From<F> for Callback<IN, OUT>
where
    F: Fn(IN) -> OUT + Send + Sync + 'static,
{
    fn from(func: F) -> Self {
        Self::new(func)
    }
}

// Additional From implementations for common patterns

/// Convert from Arc<dyn Fn> for shared callbacks
impl<IN, OUT> From<Arc<dyn Fn(IN) -> OUT + Send + Sync>> for Callback<IN, OUT> {
    fn from(func: Arc<dyn Fn(IN) -> OUT + Send + Sync>) -> Self {
        Self { callback: func }
    }
}

/// Convert from Option<Callback> - None becomes a noop callback
impl<IN> From<Option<Callback<IN>>> for Callback<IN> {
    fn from(opt: Option<Callback<IN>>) -> Self {
        opt.unwrap_or_else(Self::noop)
    }
}

impl<IN, OUT> fmt::Debug for Callback<IN, OUT> {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("Callback")
            .field("callback", &"<function>")
            .finish()
    }
}

impl<IN, OUT> PartialEq for Callback<IN, OUT> {
    fn eq(&self, other: &Self) -> bool {
        // Compare by pointer equality since we can't compare function contents
        Arc::ptr_eq(&self.callback, &other.callback)
    }
}

/// Trait for types that can be converted into event callbacks
/// This allows for flexible callback prop handling in components
pub trait IntoCallback<IN, OUT = ()> {
    fn into_callback(self) -> Callback<IN, OUT>;
}

impl<IN, OUT, F> IntoCallback<IN, OUT> for F
where
    F: Fn(IN) -> OUT + Send + Sync + 'static,
{
    fn into_callback(self) -> Callback<IN, OUT> {
        Callback::from(self)
    }
}

impl<IN, OUT> IntoCallback<IN, OUT> for Callback<IN, OUT> {
    fn into_callback(self) -> Callback<IN, OUT> {
        self
    }
}

/// Trait for converting callbacks into props that can be either Option<Callback> or Callback
/// This handles the automatic wrapping for optional callback props in the rsx! macro
pub trait IntoCallbackProp<T> {
    fn into_callback_prop(self) -> T;
}

// Implementation for non-optional callback props (Callback<IN, OUT>)
impl<F, IN, OUT> IntoCallbackProp<Callback<IN, OUT>> for F
where
    F: IntoCallback<IN, OUT>,
{
    fn into_callback_prop(self) -> Callback<IN, OUT> {
        self.into_callback()
    }
}

// Implementation for optional callback props (Option<Callback<IN, OUT>>)
impl<F, IN, OUT> IntoCallbackProp<Option<Callback<IN, OUT>>> for F
where
    F: IntoCallback<IN, OUT>,
{
    fn into_callback_prop(self) -> Option<Callback<IN, OUT>> {
        Some(self.into_callback())
    }
}

impl<IN, OUT> Callback<IN, OUT>
where
    IN: 'static,
{
    /// Create a callback from a closure with explicit type annotation
    /// This helps with type inference in complex scenarios
    pub fn from_fn<F>(func: F) -> Self
    where
        F: Fn(IN) -> OUT + Send + Sync + 'static,
    {
        Self::from(func)
    }

    /// Create a callback that ignores its input and returns a constant value
    pub fn constant(value: OUT) -> Self
    where
        OUT: Clone + Send + Sync + 'static,
    {
        Self::from(move |_| value.clone())
    }

    /// Create a callback that just prints its input (useful for debugging)
    pub fn debug() -> Self
    where
        IN: std::fmt::Debug,
        OUT: Default,
    {
        Self::from(|input| {
            println!("Callback called with: {:?}", input);
            OUT::default()
        })
    }

    /// Chain this callback with another callback
    /// The output of this callback becomes the input of the next
    pub fn then<F, NextOut>(self, next: F) -> Callback<IN, NextOut>
    where
        F: Fn(OUT) -> NextOut + Send + Sync + 'static,
        OUT: 'static,
    {
        Callback::from(move |input| {
            let intermediate = self.emit(input);
            next(intermediate)
        })
    }

    /// Map the output of this callback to a different type
    pub fn map<F, NewOut>(self, mapper: F) -> Callback<IN, NewOut>
    where
        F: Fn(OUT) -> NewOut + Send + Sync + 'static,
        OUT: 'static,
    {
        self.then(mapper)
    }

    /// Create a callback that calls this callback only if a condition is met
    pub fn filter<F>(self, predicate: F) -> Callback<IN, Option<OUT>>
    where
        F: Fn(&IN) -> bool + Send + Sync + 'static,
        IN: Clone + 'static,
        OUT: 'static,
    {
        Callback::from(move |input| {
            if predicate(&input) {
                Some(self.emit(input))
            } else {
                None
            }
        })
    }

    /// Create a callback that catches panics and returns a Result
    pub fn catch_unwind(self) -> Callback<IN, Result<OUT, String>>
    where
        IN: 'static,
        OUT: 'static,
    {
        use std::panic::{AssertUnwindSafe, catch_unwind};
        Callback::from(move |input| {
            catch_unwind(AssertUnwindSafe(|| self.emit(input)))
                .map_err(|_| "Callback panicked".to_string())
        })
    }
}

// Additional convenience constructors
impl<IN, OUT> Callback<IN, OUT>
where
    IN: 'static,
{
    /// Create a callback that always returns the same value, ignoring input
    pub fn always(value: OUT) -> Self
    where
        OUT: Clone + Send + Sync + 'static,
    {
        Self::constant(value)
    }

    /// Create a callback from a mutable closure using interior mutability
    pub fn from_mut<F>(func: F) -> Self
    where
        F: FnMut(IN) -> OUT + Send + Sync + 'static,
    {
        use std::sync::Mutex;
        let func = Mutex::new(func);
        Self::from(move |input| {
            let mut guard = func.lock().unwrap();
            guard(input)
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::{Arc, Mutex};

    #[test]
    fn test_callback_basic() {
        let result = Arc::new(Mutex::new(String::new()));
        let result_clone = result.clone();

        let callback: Callback<String> = Callback::from(move |msg: String| {
            *result_clone.lock().unwrap() = msg;
        });

        callback.emit("Hello".to_string());
        assert_eq!(*result.lock().unwrap(), "Hello");
    }

    #[test]
    fn test_callback_with_return() {
        let callback: Callback<i32, String> = Callback::from(|num: i32| format!("Number: {}", num));

        let result = callback.emit(42);
        assert_eq!(result, "Number: 42");
    }

    #[test]
    fn test_callback_reform() {
        let result = Arc::new(Mutex::new(String::new()));
        let result_clone = result.clone();

        let original: Callback<String> = Callback::from(move |msg: String| {
            *result_clone.lock().unwrap() = msg;
        });

        let reformed: Callback<i32> = original.reform(|num: i32| format!("Number: {}", num));
        reformed.emit(42);

        assert_eq!(*result.lock().unwrap(), "Number: 42");
    }

    #[test]
    fn test_callback_filter_reform() {
        let result = Arc::new(Mutex::new(String::new()));
        let result_clone = result.clone();

        let original: Callback<String> = Callback::from(move |msg: String| {
            *result_clone.lock().unwrap() = msg;
        });

        let filtered: Callback<i32, Option<()>> = original.filter_reform(|num: i32| {
            if num > 0 {
                Some(format!("Positive: {}", num))
            } else {
                None
            }
        });

        let result1 = filtered.emit(42);
        assert_eq!(*result.lock().unwrap(), "Positive: 42");
        assert!(result1.is_some());

        result.lock().unwrap().clear();
        let result2 = filtered.emit(-1);
        assert_eq!(*result.lock().unwrap(), "");
        assert!(result2.is_none());
    }

    #[test]
    fn test_callback_noop() {
        let callback: Callback<String> = Callback::noop();
        // Should not panic
        callback.emit("test".to_string());
    }

    #[test]
    fn test_callback_equality() {
        let func = |_: i32| {};
        let callback1: Callback<i32> = Callback::from(func);
        let callback2 = callback1.clone();
        let callback3: Callback<i32> = Callback::from(|_: i32| {});

        assert_eq!(callback1, callback2);
        assert_ne!(callback1, callback3);
    }

    #[test]
    fn test_into_callback_trait() {
        // Test that closures can be converted via IntoCallback
        let closure = |x: i32| x * 2;
        let callback: Callback<i32, i32> = closure.into_callback();

        assert_eq!(callback.emit(5), 10);
    }

    #[test]
    fn test_callback_from_fn() {
        let callback = Callback::from_fn(|x: String| x.len());
        assert_eq!(callback.emit("hello".to_string()), 5);
    }

    #[test]
    fn test_callback_constant() {
        let callback: Callback<i32, String> = Callback::constant("always this".to_string());
        assert_eq!(callback.emit(42), "always this");
        assert_eq!(callback.emit(100), "always this");
    }

    #[test]
    fn test_callback_then() {
        let double: Callback<i32, i32> = Callback::from(|x| x * 2);
        let to_string: Callback<i32, String> = double.then(|x| format!("Result: {}", x));

        assert_eq!(to_string.emit(5), "Result: 10");
    }

    #[test]
    fn test_callback_map() {
        let add_one: Callback<i32, i32> = Callback::from(|x| x + 1);
        let as_string: Callback<i32, String> = add_one.map(|x| x.to_string());

        assert_eq!(as_string.emit(5), "6");
    }

    #[test]
    fn test_callback_filter() {
        let double: Callback<i32, i32> = Callback::from(|x| x * 2);
        let positive_only: Callback<i32, Option<i32>> = double.filter(|&x| x > 0);

        assert_eq!(positive_only.emit(5), Some(10));
        assert_eq!(positive_only.emit(-3), None);
    }

    #[test]
    fn test_callback_from_arc() {
        let func: Arc<dyn Fn(i32) -> String + Send + Sync> = Arc::new(|x| format!("Value: {}", x));
        let callback: Callback<i32, String> = Callback::from(func);

        assert_eq!(callback.emit(42), "Value: 42");
    }

    #[test]
    fn test_callback_from_option() {
        let some_callback: Callback<i32> = Callback::from(Some(Callback::from(|_| {})));
        let none_callback: Callback<i32> = Callback::from(None::<Callback<i32>>);

        // Both should work without panicking
        some_callback.emit(1);
        none_callback.emit(1);
    }

    #[test]
    fn test_callback_always() {
        let callback: Callback<String, i32> = Callback::always(42);
        assert_eq!(callback.emit("anything".to_string()), 42);
        assert_eq!(callback.emit("else".to_string()), 42);
    }

    #[test]
    fn test_callback_from_mut() {
        let mut counter = 0;
        let callback: Callback<i32, i32> = Callback::from_mut(move |x| {
            counter += 1;
            x + counter
        });

        assert_eq!(callback.emit(10), 11); // 10 + 1
        assert_eq!(callback.emit(10), 12); // 10 + 2
    }

    #[test]
    fn test_callback_catch_unwind() {
        let safe_callback: Callback<i32, i32> = Callback::from(|x| x * 2);
        let panic_callback: Callback<i32, i32> = Callback::from(|_| panic!("Test panic"));

        let safe_wrapped = safe_callback.catch_unwind();
        let panic_wrapped = panic_callback.catch_unwind();

        assert_eq!(safe_wrapped.emit(5), Ok(10));
        assert!(panic_wrapped.emit(5).is_err());
    }
}
