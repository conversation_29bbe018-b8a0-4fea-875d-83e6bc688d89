//! # Advanced Future Hooks Showcase
//!
//! This example demonstrates the complete async capabilities of the rink-leptos framework
//! through comprehensive use of the `use_future` hook system. It showcases professional
//! patterns for building reactive, async TUI applications.
//!
//! ## 🚀 Features Demonstrated:
//! - **Basic Async Operations**: Simple future execution with clean state management
//! - **Progress Tracking**: Real-time progress updates for long-running operations
//! - **Error Handling**: Robust error recovery and retry mechanisms
//! - **Dependency Management**: Automatic re-execution based on state changes
//! - **Concurrent Operations**: Managing multiple async tasks simultaneously
//! - **Real-World Patterns**: API calls, data processing, and workflow management
//! - **Professional UI**: Beautiful, responsive interface with RSX components
//!
//! ## 🎯 Usage:
//! Run with: `cargo run --bin use_future_example`

use crossterm::event::{Event, KeyCode};
use ratatui::{layout::Constraint, prelude::Direction};
use rink::prelude::*;
use rink_core::{
    hooks::future::{FutureHandle, FutureState, use_future, use_future_with_progress},
    panic_handler::setup_panic_handler,
};
use std::time::{Duration, Instant};

/// Application state for managing different demo sections
#[derive(Clone, PartialEq)]
enum DemoSection {
    Overview,
    BasicFuture,
    ProgressTracking,
    ErrorHandling,
    DependencyDriven,
    ConcurrentOps,
    RealWorldAPI,
}

impl DemoSection {
    fn next(&self) -> Self {
        match self {
            DemoSection::Overview => DemoSection::BasicFuture,
            DemoSection::BasicFuture => DemoSection::ProgressTracking,
            DemoSection::ProgressTracking => DemoSection::ErrorHandling,
            DemoSection::ErrorHandling => DemoSection::DependencyDriven,
            DemoSection::DependencyDriven => DemoSection::ConcurrentOps,
            DemoSection::ConcurrentOps => DemoSection::RealWorldAPI,
            DemoSection::RealWorldAPI => DemoSection::Overview,
        }
    }

    fn prev(&self) -> Self {
        match self {
            DemoSection::Overview => DemoSection::RealWorldAPI,
            DemoSection::BasicFuture => DemoSection::Overview,
            DemoSection::ProgressTracking => DemoSection::BasicFuture,
            DemoSection::ErrorHandling => DemoSection::ProgressTracking,
            DemoSection::DependencyDriven => DemoSection::ErrorHandling,
            DemoSection::ConcurrentOps => DemoSection::DependencyDriven,
            DemoSection::RealWorldAPI => DemoSection::ConcurrentOps,
        }
    }

    fn title(&self) -> &'static str {
        match self {
            DemoSection::Overview => "🏠 Future Hooks Overview",
            DemoSection::BasicFuture => "⚡ Basic Future Operations",
            DemoSection::ProgressTracking => "📊 Progress Tracking",
            DemoSection::ErrorHandling => "🛡️ Error Handling & Recovery",
            DemoSection::DependencyDriven => "🔄 Dependency-Driven Updates",
            DemoSection::ConcurrentOps => "🔀 Concurrent Operations",
            DemoSection::RealWorldAPI => "🌐 Real-World API Patterns",
        }
    }
}

/// Main application component
fn build_ui() -> VNode {
    let (current_section, set_current_section) = use_state(DemoSection::Overview);
    let (app_start_time, set_app_start_time) = use_state(None::<Instant>);

    // Initialize app start time on first render
    if app_start_time.get().is_none() {
        set_app_start_time.set(Some(Instant::now()));
    }

    // Initialize all hook states at the top level to maintain consistent hook order
    let (refresh_trigger, set_refresh_trigger) = use_state(0);
    let (start_download, set_start_download) = use_state(0);
    let (attempt_count, set_attempt_count) = use_state(0);
    let (force_error, set_force_error) = use_state(false);
    let (user_id, set_user_id) = use_state(1);
    let (include_details, set_include_details) = use_state(false);
    let (start_batch, set_start_batch) = use_state(0);
    let (workflow_step, set_workflow_step) = use_state(0);
    let (_, controller) = use_app();

    // Handle navigation events
    if let Some(Event::Key(key)) = use_event() {
        if key.is_press() {
            match key.code {
                KeyCode::Right | KeyCode::Char('n') => {
                    set_current_section.update(|section| section.next());
                }
                KeyCode::Left | KeyCode::Char('p') => {
                    set_current_section.update(|section| section.prev());
                }
                KeyCode::Char('q') | KeyCode::Esc => {
                    controller.shutdown();
                }
                // Handle section-specific keys
                KeyCode::Char('r') if current_section.get() == DemoSection::BasicFuture => {
                    set_refresh_trigger.update(|count| *count + 1);
                }
                KeyCode::Char('d') if current_section.get() == DemoSection::ProgressTracking => {
                    set_start_download.update(|count| *count + 1);
                }
                KeyCode::Char('a') if current_section.get() == DemoSection::ErrorHandling => {
                    set_attempt_count.update(|count| *count + 1);
                }
                KeyCode::Char('e') if current_section.get() == DemoSection::ErrorHandling => {
                    set_force_error.update(|enabled| !*enabled);
                }
                KeyCode::Char('u') if current_section.get() == DemoSection::DependencyDriven => {
                    set_user_id.update(|id| if *id >= 5 { 1 } else { *id + 1 });
                }
                KeyCode::Char('i') if current_section.get() == DemoSection::DependencyDriven => {
                    set_include_details.update(|details| !*details);
                }
                KeyCode::Char('b') if current_section.get() == DemoSection::ConcurrentOps => {
                    set_start_batch.update(|count| *count + 1);
                }
                KeyCode::Char('w') if current_section.get() == DemoSection::RealWorldAPI => {
                    set_workflow_step.update(|count| *count + 1);
                }
                _ => {}
            }
        }
    }

    // Create all futures at the top level to maintain consistent hook order
    let data_future: FutureHandle<Vec<&str>, String> = use_future(
        || async move {
            tokio::time::sleep(Duration::from_millis(800)).await;
            let data = vec![
                "🌟 User Dashboard",
                "📊 Analytics Panel",
                "⚙️ Settings Menu",
                "📝 Recent Activity",
                "🔔 Notifications",
            ];
            Ok(data)
        },
        refresh_trigger.get(),
    );

    let download_future: FutureHandle<String, String> = use_future_with_progress(
        |progress_callback| async move {
            let total_chunks = 20;
            let mut downloaded = 0;
            for chunk in 1..=total_chunks {
                tokio::time::sleep(Duration::from_millis(100)).await;
                downloaded += chunk * 1024;
                progress_callback(chunk as f32 / total_chunks as f32);
            }
            Ok(format!("Downloaded {} KB successfully!", downloaded))
        },
        start_download.get(),
    );

    let attempt_count_val = attempt_count.get();
    let force_error_val = force_error.get();
    let api_call_future: FutureHandle<String, String> = use_future(
        move || async move {
            tokio::time::sleep(Duration::from_millis(500)).await;
            if force_error_val && attempt_count_val % 3 != 0 {
                return Err("🌐 Network timeout - please try again".to_string());
            }
            Ok(format!(
                "🎉 API Success! Attempt #{}\n📊 Retrieved 42 records\n⏱️ Response time: 245ms",
                attempt_count_val + 1
            ))
        },
        (attempt_count_val, force_error_val),
    );

    let user_id_val = user_id.get();
    let include_details_val = include_details.get();
    let user_profile_future: FutureHandle<String, String> = use_future(
        move || async move {
            tokio::time::sleep(Duration::from_millis(300)).await;
            let base_profile = format!(
                "👤 User #{}\n📧 user{}@example.com",
                user_id_val, user_id_val
            );
            if include_details_val {
                Ok(format!(
                    "{}\n🏢 Software Engineer\n📍 San Francisco, CA\n⭐ Premium Member",
                    base_profile
                ))
            } else {
                Ok(base_profile)
            }
        },
        (user_id_val, include_details_val),
    );

    let start_batch_val = start_batch.get();
    let weather_future: FutureHandle<String, String> = use_future(
        || async move {
            tokio::time::sleep(Duration::from_millis(400)).await;
            Ok("🌤️ 72°F, Partly Cloudy".to_string())
        },
        start_batch_val,
    );

    let news_future: FutureHandle<String, String> = use_future(
        || async move {
            tokio::time::sleep(Duration::from_millis(600)).await;
            Ok("📰 Tech stocks rise 2.3%".to_string())
        },
        start_batch_val,
    );

    let notifications_future: FutureHandle<String, String> = use_future(
        || async move {
            tokio::time::sleep(Duration::from_millis(200)).await;
            Ok("🔔 3 new messages".to_string())
        },
        start_batch_val,
    );

    let workflow_future: FutureHandle<String, String> = use_future_with_progress(
        |progress_callback| async move {
            let steps = [
                ("🔐 Authenticating user", 200),
                ("📊 Fetching dashboard data", 400),
                ("🎨 Rendering components", 300),
                ("✨ Applying user preferences", 250),
                ("🚀 Finalizing setup", 150),
            ];
            let mut results = Vec::new();
            for (i, (step_name, duration)) in steps.iter().enumerate() {
                progress_callback(i as f32 / steps.len() as f32);
                tokio::time::sleep(Duration::from_millis(*duration)).await;
                results.push(format!("✅ {}", step_name));
            }
            progress_callback(1.0);
            Ok(format!(
                "🎉 Application Ready!\n\n{}\n\n💫 Welcome to your personalized dashboard!",
                results.join("\n")
            ))
        },
        workflow_step.get(),
    );

    let section = current_section.get();
    let uptime = app_start_time
        .get()
        .map(|start| start.elapsed().as_secs())
        .unwrap_or(0);

    rsx! {
        <Layout
            direction={Direction::Vertical}
            constraints={[
                Constraint::Length(5),    // Header: fixed height
                Constraint::Min(10),      // Content: flexible, minimum 10 lines
                Constraint::Length(3)     // Footer: fixed height
            ]}
        >
            {render_header(&section, uptime)}
            {render_content(&section, &data_future, &download_future, &api_call_future, &user_profile_future, &weather_future, &news_future, &notifications_future, &workflow_future, attempt_count_val, force_error_val, user_id_val, include_details_val)}
            {render_footer()}
        </Layout>
    }
}

/// Renders the application header with navigation
fn render_header(section: &DemoSection, uptime: u64) -> VNode {
    rsx! {
        <Block
            title="🚀 Rink-Leptos Future Hooks Showcase"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Cyan)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Center}
            >
                {format!(
                    "{}\n\n\
                    Navigation: ← → (or p/n) | Quit: q/Esc | Uptime: {}s",
                    section.title(),
                    uptime
                )}
            </Paragraph>
        </Block>
    }
}

/// Renders the main content based on current section
#[allow(clippy::too_many_arguments)]
fn render_content(
    section: &DemoSection,
    data_future: &FutureHandle<Vec<&str>, String>,
    download_future: &FutureHandle<String, String>,
    api_call_future: &FutureHandle<String, String>,
    user_profile_future: &FutureHandle<String, String>,
    weather_future: &FutureHandle<String, String>,
    news_future: &FutureHandle<String, String>,
    notifications_future: &FutureHandle<String, String>,
    workflow_future: &FutureHandle<String, String>,
    attempt_count_val: i32,
    force_error_val: bool,
    user_id_val: i32,
    include_details_val: bool,
) -> VNode {
    match section {
        DemoSection::Overview => render_overview(),
        DemoSection::BasicFuture => render_basic_future(data_future),
        DemoSection::ProgressTracking => render_progress_tracking(download_future),
        DemoSection::ErrorHandling => {
            render_error_handling(api_call_future, attempt_count_val, force_error_val)
        }
        DemoSection::DependencyDriven => {
            render_dependency_driven(user_profile_future, user_id_val, include_details_val)
        }
        DemoSection::ConcurrentOps => {
            render_concurrent_ops(weather_future, news_future, notifications_future)
        }
        DemoSection::RealWorldAPI => render_real_world_api(workflow_future),
    }
}

/// Renders the overview section
fn render_overview() -> VNode {
    rsx! {
        <Block
            title="Welcome to Future Hooks"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Green)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                wrap={Wrap { trim: true }}
            >
                {"🎯 This showcase demonstrates the powerful async capabilities of rink-leptos\n\n\
                📚 What you'll learn:\n\
                • How to handle async operations with use_future\n\
                • Progress tracking for long-running tasks\n\
                • Professional error handling patterns\n\
                • Dependency-based re-execution\n\
                • Managing concurrent operations\n\
                • Real-world API integration patterns\n\n\
                🚀 Each section includes:\n\
                • Live code examples\n\
                • Interactive demonstrations\n\
                • Professional UI patterns\n\
                • Best practices and tips\n\n\
                Navigate with arrow keys or p/n to explore each feature!"}
            </Paragraph>
        </Block>
    }
}

/// Renders the basic future operations section
fn render_basic_future(data_future: &FutureHandle<Vec<&str>, String>) -> VNode {
    let content = match data_future.state() {
        FutureState::Pending => "⏳ Loading dashboard components...".to_string(),
        FutureState::Progress(_) => "📡 Processing data...".to_string(),
        FutureState::Resolved(items) => {
            format!(
                "✅ Dashboard Ready!\n\n{}",
                items
                    .iter()
                    .enumerate()
                    .map(|(i, item)| format!("{}. {}", i + 1, item))
                    .collect::<Vec<_>>()
                    .join("\n")
            )
        }
        FutureState::Error(err) => format!("❌ Error: {}", err),
    };

    rsx! {
        <Block
            title="Basic Future Operations"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Blue)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                wrap={Wrap { trim: true }}
            >
                {format!(
                    "{}\n\n\
                    💡 This demonstrates:\n\
                    • Simple async operations with use_future\n\
                    • Automatic state management (Pending → Resolved)\n\
                    • Clean error handling patterns\n\
                    • Dependency-based re-execution\n\n\
                    🔄 Press 'r' to refresh the data",
                    content
                )}
            </Paragraph>
        </Block>
    }
}

/// Renders the progress tracking section
fn render_progress_tracking(download_future: &FutureHandle<String, String>) -> VNode {
    let content = match download_future.state() {
        FutureState::Pending => "📁 Ready to download...".to_string(),
        FutureState::Progress(progress) => {
            let percentage = (progress * 100.0) as u32;
            let bar_length = 30;
            let filled = ((progress * bar_length as f32) as usize).min(bar_length);
            let empty = bar_length - filled;

            format!(
                "📥 Downloading... {}%\n[{}{}]",
                percentage,
                "█".repeat(filled),
                "░".repeat(empty)
            )
        }
        FutureState::Resolved(result) => format!("✅ {}", result),
        FutureState::Error(err) => format!("❌ Download failed: {}", err),
    };

    rsx! {
        <Block
            title="Progress Tracking"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Magenta)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                wrap={Wrap { trim: true }}
            >
                {format!(
                    "{}\n\n\
                    💡 This demonstrates:\n\
                    • Real-time progress updates during async operations\n\
                    • Visual progress bars with percentage indicators\n\
                    • Smooth UI updates without blocking\n\
                    • Professional user feedback patterns\n\n\
                    📥 Press 'd' to start download",
                    content
                )}
            </Paragraph>
        </Block>
    }
}

/// Renders the error handling section
fn render_error_handling(
    api_call_future: &FutureHandle<String, String>,
    attempt_count_val: i32,
    force_error_val: bool,
) -> VNode {
    let content = match api_call_future.state() {
        FutureState::Pending => "🔄 Calling API...".to_string(),
        FutureState::Progress(_) => "📡 Processing request...".to_string(),
        FutureState::Resolved(result) => result,
        FutureState::Error(err) => {
            format!(
                "{}\n🔄 Retry available (attempt #{})",
                err,
                attempt_count_val + 1
            )
        }
    };

    rsx! {
        <Block
            title="Error Handling & Recovery"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Red)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                wrap={Wrap { trim: true }}
            >
                {format!(
                    "{}\n\n\
                    💡 This demonstrates:\n\
                    • Robust error handling with detailed messages\n\
                    • Retry mechanisms and recovery patterns\n\
                    • User-friendly error feedback\n\
                    • Graceful degradation strategies\n\n\
                    🔄 Press 'a' to call API | ❌ Press 'e' to toggle errors ({})",
                    content,
                    if force_error_val { "ON" } else { "OFF" }
                )}
            </Paragraph>
        </Block>
    }
}

/// Renders the dependency-driven section
fn render_dependency_driven(
    user_profile_future: &FutureHandle<String, String>,
    user_id_val: i32,
    include_details_val: bool,
) -> VNode {
    let content = match user_profile_future.state() {
        FutureState::Pending => "👤 Loading user profile...".to_string(),
        FutureState::Progress(_) => "🔍 Fetching user data...".to_string(),
        FutureState::Resolved(profile) => profile,
        FutureState::Error(err) => format!("❌ Failed to load profile: {}", err),
    };

    rsx! {
        <Block
            title="Dependency-Driven Updates"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Yellow)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                wrap={Wrap { trim: true }}
            >
                {format!(
                    "{}\n\n\
                    💡 This demonstrates:\n\
                    • Automatic re-execution when dependencies change\n\
                    • Efficient caching and memoization\n\
                    • Reactive programming patterns\n\
                    • Smart dependency tracking\n\n\
                    Current: User {} | Details: {}\n\
                    ⬆️ Press 'u' for next user | ℹ️ Press 'i' to toggle details",
                    content,
                    user_id_val,
                    if include_details_val { "ON" } else { "OFF" }
                )}
            </Paragraph>
        </Block>
    }
}

/// Renders the concurrent operations section
fn render_concurrent_ops(
    weather_future: &FutureHandle<String, String>,
    news_future: &FutureHandle<String, String>,
    notifications_future: &FutureHandle<String, String>,
) -> VNode {
    let weather_status = format_future_status("Weather", weather_future);
    let news_status = format_future_status("News", news_future);
    let notifications_status = format_future_status("Notifications", notifications_future);

    rsx! {
        <Block
            title="Concurrent Operations"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Cyan)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                wrap={Wrap { trim: true }}
            >
                {format!(
                    "{}\n{}\n{}\n\n\
                    💡 This demonstrates:\n\
                    • Managing multiple async operations simultaneously\n\
                    • Independent future lifecycles and states\n\
                    • Efficient resource utilization\n\
                    • Non-blocking concurrent execution\n\n\
                    🚀 Press 'b' to start batch operations",
                    weather_status,
                    news_status,
                    notifications_status
                )}
            </Paragraph>
        </Block>
    }
}

/// Renders the real-world API section
fn render_real_world_api(workflow_future: &FutureHandle<String, String>) -> VNode {
    let content = match workflow_future.state() {
        FutureState::Pending => "⏳ Initializing application...".to_string(),
        FutureState::Progress(progress) => {
            let percentage = (progress * 100.0) as u32;
            format!("🔄 Setting up your experience... {}%", percentage)
        }
        FutureState::Resolved(result) => result,
        FutureState::Error(err) => format!("💥 Setup failed: {}", err),
    };

    rsx! {
        <Block
            title="Real-World API Patterns"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Green)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                wrap={Wrap { trim: true }}
            >
                {format!(
                    "{}\n\n\
                    💡 This demonstrates:\n\
                    • Complex multi-step workflows\n\
                    • Progress tracking through multiple phases\n\
                    • Professional application initialization\n\
                    • Real-world async patterns and best practices\n\n\
                    🌟 Press 'w' to start application workflow",
                    content
                )}
            </Paragraph>
        </Block>
    }
}

/// Helper function to format future status for concurrent operations display
fn format_future_status<T, E>(name: &str, future: &FutureHandle<T, E>) -> String
where
    T: std::fmt::Display + Clone,
    E: std::fmt::Display + Clone,
{
    match future.state() {
        FutureState::Pending => format!("⏳ {}: Loading...", name),
        FutureState::Progress(p) => format!("🔄 {}: {}%", name, (p * 100.0) as u32),
        FutureState::Resolved(data) => format!("✅ {}: {}", name, data),
        FutureState::Error(err) => format!("❌ {}: {}", name, err),
    }
}

/// Renders the application footer
fn render_footer() -> VNode {
    rsx! {
        <Block
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::DarkGray)}
        >
            <Paragraph
                style={Style::default().fg(Color::DarkGray)}
                alignment={Alignment::Center}
            >
                {"🚀 Rink-Leptos Future Hooks Showcase | Built with ❤️ using RSX"}
            </Paragraph>
        </Block>
    }
}

/// Entry point of the application
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize the panic handler
    setup_panic_handler();

    render_async(build_ui).await
}
