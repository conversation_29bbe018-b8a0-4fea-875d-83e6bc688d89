//! Local storage hook for persistent state management
//!
//! This module provides a `use_local_storage` hook that enables persistent state
//! management by synchronizing with file-based storage. It's designed to work
//! in terminal applications where browser localStorage is not available.
//!
//! The hook provides:
//! - Reactive signals that synchronize with file storage
//! - Type-safe serialization/deserialization using serde
//! - Proper error handling for storage failures
//! - Automatic cleanup and memory management
//! - Support for both primitive and complex serializable types
//! - Thread-safe operations for concurrent access

use crate::hooks::state::StateHandle;
use parking_lot::RwLock;
use serde::{Deserialize, Serialize};
use std::any::Any;
use std::collections::HashMap;
use std::fs;
use std::path::PathBuf;
use std::sync::{Arc, OnceLock};

#[cfg(test)]
mod tests;

/// Errors that can occur during local storage operations
#[derive(Debug, Clone)]
pub enum LocalStorageError {
    /// Failed to serialize value to JSON
    SerializationError(String),
    /// Failed to deserialize value from JSON
    DeserializationError(String),
    /// Failed to read from storage file
    ReadError(String),
    /// Failed to write to storage file
    WriteError(String),
    /// Storage directory creation failed
    DirectoryCreationError(String),
    /// Storage is not available (e.g., in SSR context)
    StorageUnavailable,
}

impl std::fmt::Display for LocalStorageError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            LocalStorageError::SerializationError(msg) => {
                write!(f, "Serialization error: {}", msg)
            }
            LocalStorageError::DeserializationError(msg) => {
                write!(f, "Deserialization error: {}", msg)
            }
            LocalStorageError::ReadError(msg) => write!(f, "Read error: {}", msg),
            LocalStorageError::WriteError(msg) => write!(f, "Write error: {}", msg),
            LocalStorageError::DirectoryCreationError(msg) => {
                write!(f, "Directory creation error: {}", msg)
            }
            LocalStorageError::StorageUnavailable => {
                write!(f, "Storage is not available in this context")
            }
        }
    }
}

impl std::error::Error for LocalStorageError {}

/// Result type for local storage operations
pub type LocalStorageResult<T> = Result<T, LocalStorageError>;

/// Configuration for local storage behavior
#[derive(Debug, Clone)]
pub struct LocalStorageConfig {
    /// Base directory for storage files
    pub storage_dir: PathBuf,
    /// Whether to create the storage directory if it doesn't exist
    pub create_dir: bool,
    /// File extension for storage files
    pub file_extension: String,
    /// Whether to pretty-print JSON (useful for debugging)
    pub pretty_json: bool,
}

impl Default for LocalStorageConfig {
    fn default() -> Self {
        Self {
            storage_dir: PathBuf::from(".local_storage"),
            create_dir: true,
            file_extension: "json".to_string(),
            pretty_json: false,
        }
    }
}

/// Global configuration for local storage
static STORAGE_CONFIG: OnceLock<RwLock<LocalStorageConfig>> = OnceLock::new();

/// Set the global storage configuration
///
/// This function allows you to configure the storage directory and other
/// settings for all local storage hooks in your application.
///
/// # Examples
///
/// ```rust,no_run
/// use rink_core::hooks::storage::{set_storage_config, LocalStorageConfig};
/// use std::path::PathBuf;
///
/// // Configure storage to use a custom directory
/// set_storage_config(LocalStorageConfig {
///     storage_dir: PathBuf::from("./app_data"),
///     create_dir: true,
///     file_extension: "json".to_string(),
///     pretty_json: true,
/// });
/// ```
pub fn set_storage_config(config: LocalStorageConfig) {
    let config_lock = STORAGE_CONFIG.get_or_init(|| RwLock::new(LocalStorageConfig::default()));
    *config_lock.write() = config;
}

/// Get the current storage configuration
fn get_storage_config() -> LocalStorageConfig {
    let config_lock = STORAGE_CONFIG.get_or_init(|| RwLock::new(LocalStorageConfig::default()));
    config_lock.read().clone()
}

/// Storage backend trait for abstracting storage operations
///
/// This trait allows for different storage implementations (file-based, in-memory, etc.)
/// while maintaining a consistent interface for the local storage hook.
pub trait StorageBackend: Send + Sync {
    /// Read a value from storage
    fn read(&self, key: &str) -> LocalStorageResult<Option<String>>;

    /// Write a value to storage
    fn write(&self, key: &str, value: &str) -> LocalStorageResult<()>;

    /// Remove a value from storage
    fn remove(&self, key: &str) -> LocalStorageResult<()>;

    /// Check if storage is available
    fn is_available(&self) -> bool;
}

/// File-based storage backend
#[derive(Debug)]
pub struct FileStorageBackend {
    config: LocalStorageConfig,
}

impl FileStorageBackend {
    /// Create a new file storage backend with the given configuration
    pub fn new(config: LocalStorageConfig) -> Self {
        Self { config }
    }

    /// Get the file path for a given key
    fn get_file_path(&self, key: &str) -> PathBuf {
        let filename = format!("{}.{}", key, self.config.file_extension);
        self.config.storage_dir.join(filename)
    }

    /// Ensure the storage directory exists
    fn ensure_storage_dir(&self) -> LocalStorageResult<()> {
        if !self.config.storage_dir.exists() && self.config.create_dir {
            fs::create_dir_all(&self.config.storage_dir).map_err(|e| {
                LocalStorageError::DirectoryCreationError(format!(
                    "Failed to create storage directory '{}': {}",
                    self.config.storage_dir.display(),
                    e
                ))
            })?;
        }
        Ok(())
    }
}

impl StorageBackend for FileStorageBackend {
    fn read(&self, key: &str) -> LocalStorageResult<Option<String>> {
        let file_path = self.get_file_path(key);

        if !file_path.exists() {
            return Ok(None);
        }

        fs::read_to_string(&file_path).map(Some).map_err(|e| {
            LocalStorageError::ReadError(format!(
                "Failed to read storage file '{}': {}",
                file_path.display(),
                e
            ))
        })
    }

    fn write(&self, key: &str, value: &str) -> LocalStorageResult<()> {
        self.ensure_storage_dir()?;

        let file_path = self.get_file_path(key);
        fs::write(&file_path, value).map_err(|e| {
            LocalStorageError::WriteError(format!(
                "Failed to write storage file '{}': {}",
                file_path.display(),
                e
            ))
        })
    }

    fn remove(&self, key: &str) -> LocalStorageResult<()> {
        let file_path = self.get_file_path(key);

        if file_path.exists() {
            fs::remove_file(&file_path).map_err(|e| {
                LocalStorageError::WriteError(format!(
                    "Failed to remove storage file '{}': {}",
                    file_path.display(),
                    e
                ))
            })?;
        }

        Ok(())
    }

    fn is_available(&self) -> bool {
        // Check if we can create the storage directory or if it already exists
        if self.config.storage_dir.exists() {
            return true;
        }

        if self.config.create_dir {
            // Try to create the directory to test availability
            if let Ok(()) = fs::create_dir_all(&self.config.storage_dir) {
                return true;
            }
        }

        false
    }
}

/// In-memory storage backend for testing and development
#[derive(Debug, Default)]
pub struct MemoryStorageBackend {
    storage: RwLock<HashMap<String, String>>,
}

impl MemoryStorageBackend {
    /// Create a new in-memory storage backend
    pub fn new() -> Self {
        Self::default()
    }

    /// Clear all stored values
    pub fn clear(&self) {
        self.storage.write().clear();
    }

    /// Get the number of stored items
    pub fn len(&self) -> usize {
        self.storage.read().len()
    }

    /// Check if the storage is empty
    pub fn is_empty(&self) -> bool {
        self.storage.read().is_empty()
    }
}

impl StorageBackend for MemoryStorageBackend {
    fn read(&self, key: &str) -> LocalStorageResult<Option<String>> {
        Ok(self.storage.read().get(key).cloned())
    }

    fn write(&self, key: &str, value: &str) -> LocalStorageResult<()> {
        self.storage
            .write()
            .insert(key.to_string(), value.to_string());
        Ok(())
    }

    fn remove(&self, key: &str) -> LocalStorageResult<()> {
        self.storage.write().remove(key);
        Ok(())
    }

    fn is_available(&self) -> bool {
        true
    }
}

/// Local storage handle that provides access to stored values
///
/// This handle provides a reactive interface to local storage, automatically
/// synchronizing with the underlying storage backend when values change.
pub struct LocalStorageHandle<T> {
    /// The current state handle
    state: StateHandle<T>,
    /// Storage key for this value
    key: String,
    /// Storage backend
    backend: Arc<dyn StorageBackend>,
    /// Configuration for serialization
    config: LocalStorageConfig,
}

impl<T> LocalStorageHandle<T>
where
    T: Clone + Serialize + for<'de> Deserialize<'de> + Send + Sync + 'static,
{
    /// Create a new local storage handle
    fn new(
        state: StateHandle<T>,
        key: String,
        backend: Arc<dyn StorageBackend>,
        config: LocalStorageConfig,
    ) -> Self {
        Self {
            state,
            key,
            backend,
            config,
        }
    }

    /// Get the current value
    pub fn get(&self) -> T {
        self.state.get()
    }

    /// Get the storage key
    pub fn key(&self) -> &str {
        &self.key
    }

    /// Check if storage is available
    pub fn is_storage_available(&self) -> bool {
        self.backend.is_available()
    }

    /// Load value from storage, returning None if not found or on error
    pub fn load_from_storage(&self) -> Option<T> {
        if !self.backend.is_available() {
            return None;
        }

        match self.backend.read(&self.key) {
            Ok(Some(json_str)) => serde_json::from_str::<T>(&json_str).ok(),
            _ => None, // Silently ignore read errors
        }
    }

    /// Save current value to storage
    pub fn save_to_storage(&self) -> LocalStorageResult<()> {
        if !self.backend.is_available() {
            return Err(LocalStorageError::StorageUnavailable);
        }

        let current_value = self.get();
        let json_str = if self.config.pretty_json {
            serde_json::to_string_pretty(&current_value)
        } else {
            serde_json::to_string(&current_value)
        }
        .map_err(|e| LocalStorageError::SerializationError(e.to_string()))?;

        self.backend.write(&self.key, &json_str)
    }

    /// Remove value from storage
    pub fn remove_from_storage(&self) -> LocalStorageResult<()> {
        if !self.backend.is_available() {
            return Err(LocalStorageError::StorageUnavailable);
        }

        self.backend.remove(&self.key)
    }
}

impl<T> Clone for LocalStorageHandle<T>
where
    T: Clone + Serialize + for<'de> Deserialize<'de> + Send + Sync + 'static,
{
    fn clone(&self) -> Self {
        Self {
            state: self.state.clone(),
            key: self.key.clone(),
            backend: self.backend.clone(),
            config: self.config.clone(),
        }
    }
}

/// Local storage setter that automatically persists changes
pub struct LocalStorageSetter<T> {
    /// Storage handle for accessing storage operations
    handle: LocalStorageHandle<T>,
    /// State setter for updating the reactive state
    state_setter: crate::hooks::state::StateSetter<T>,
}

impl<T> LocalStorageSetter<T>
where
    T: Clone + Serialize + for<'de> Deserialize<'de> + Send + Sync + 'static,
{
    /// Create a new local storage setter
    fn new(
        handle: LocalStorageHandle<T>,
        state_setter: crate::hooks::state::StateSetter<T>,
    ) -> Self {
        Self {
            handle,
            state_setter,
        }
    }

    /// Set a new value and persist it to storage
    pub fn set(&self, new_value: T) {
        // Update the reactive state first
        self.state_setter.set(new_value);

        // Then persist to storage (ignore errors to maintain reactivity)
        let _ = self.handle.save_to_storage();
    }

    /// Update the value using a function and persist to storage
    pub fn update<F>(&self, updater: F)
    where
        F: FnOnce(&T) -> T,
    {
        // Update the reactive state first
        self.state_setter.update(updater);

        // Then persist to storage (ignore errors to maintain reactivity)
        let _ = self.handle.save_to_storage();
    }

    /// Set a new value without persisting to storage
    ///
    /// This is useful for temporary updates that shouldn't be saved
    pub fn set_temporary(&self, new_value: T) {
        self.state_setter.set(new_value);
    }

    /// Update the value using a function without persisting to storage
    ///
    /// This is useful for temporary updates that shouldn't be saved
    pub fn update_temporary<F>(&self, updater: F)
    where
        F: FnOnce(&T) -> T,
    {
        self.state_setter.update(updater);
    }

    /// Manually save the current value to storage
    pub fn save(&self) -> LocalStorageResult<()> {
        self.handle.save_to_storage()
    }

    /// Remove the value from storage (but keep the in-memory state)
    pub fn remove_from_storage(&self) -> LocalStorageResult<()> {
        self.handle.remove_from_storage()
    }

    /// Get access to the storage handle for advanced operations
    pub fn handle(&self) -> &LocalStorageHandle<T> {
        &self.handle
    }
}

impl<T> Clone for LocalStorageSetter<T>
where
    T: Clone + Serialize + for<'de> Deserialize<'de> + Send + Sync + 'static,
{
    fn clone(&self) -> Self {
        Self {
            handle: self.handle.clone(),
            state_setter: self.state_setter.clone(),
        }
    }
}

/// Global storage backend registry for managing storage instances
static STORAGE_BACKEND: OnceLock<RwLock<Arc<dyn StorageBackend>>> = OnceLock::new();

/// Global registry for storage state containers to ensure key uniqueness
static STORAGE_STATES: OnceLock<RwLock<HashMap<String, Box<dyn Any + Send + Sync>>>> =
    OnceLock::new();

/// Set the global storage backend
///
/// This function allows you to configure the storage backend used by all
/// local storage hooks in your application. By default, a file-based backend
/// is used with the default configuration.
///
/// # Examples
///
/// ```rust,no_run
/// use rink_core::hooks::storage::{set_storage_backend, FileStorageBackend, LocalStorageConfig};
/// use std::sync::Arc;
///
/// // Use a custom file-based backend
/// let config = LocalStorageConfig::default();
/// let backend = Arc::new(FileStorageBackend::new(config));
/// set_storage_backend(backend);
/// ```
pub fn set_storage_backend(backend: Arc<dyn StorageBackend>) {
    let backend_lock = STORAGE_BACKEND.get_or_init(|| {
        let default_config = LocalStorageConfig::default();
        let default_backend = Arc::new(FileStorageBackend::new(default_config));
        RwLock::new(default_backend)
    });
    *backend_lock.write() = backend;
}

/// Get the current storage backend
fn get_storage_backend() -> Arc<dyn StorageBackend> {
    let backend_lock = STORAGE_BACKEND.get_or_init(|| {
        let default_config = get_storage_config();
        let default_backend = Arc::new(FileStorageBackend::new(default_config));
        RwLock::new(default_backend)
    });
    backend_lock.read().clone()
}

/// Clear all global storage state (for testing)
#[cfg(test)]
pub fn clear_storage_state() {
    if let Some(states_registry) = STORAGE_STATES.get() {
        states_registry.write().clear();
    }
}

/// Professional-grade local storage hook for persistent state management
///
/// This hook provides a reactive interface to persistent storage, automatically
/// synchronizing state with file-based storage. It follows Leptos/React patterns
/// while providing robust error handling and type safety.
///
/// # Features
///
/// - **Reactive State**: Returns a signal that automatically updates when storage changes
/// - **Type Safety**: Full generic support with serde serialization/deserialization
/// - **Error Handling**: Graceful handling of storage failures without breaking reactivity
/// - **Thread Safety**: Safe for use in concurrent contexts
/// - **Automatic Persistence**: Changes are automatically saved to storage
/// - **SSR Compatible**: Handles server-side rendering contexts gracefully
/// - **Memory Management**: Proper cleanup and no memory leaks
///
/// # Parameters
///
/// - `key`: Storage key (String) - unique identifier for the stored value
/// - `default_value`: Default value to use if storage is empty or unavailable
///
/// # Returns
///
/// Returns a tuple of `(LocalStorageHandle<T>, LocalStorageSetter<T>)`:
/// - `LocalStorageHandle<T>`: Provides read access and storage operations
/// - `LocalStorageSetter<T>`: Provides write access with automatic persistence
///
/// # Examples
///
/// ## Basic Usage with Primitives
///
/// ```rust,no_run
/// use rink_core::hooks::storage::use_local_storage;
/// use serde::{Deserialize, Serialize};
///
/// // In a component context:
/// let (count_handle, set_count) = use_local_storage("counter".to_string(), 0i32);
///
/// // Read the current value
/// let current_count = count_handle.get();
/// println!("Current count: {}", current_count);
///
/// // Update the value (automatically persisted)
/// set_count.set(42);
///
/// // Functional update
/// set_count.update(|prev| prev + 1);
/// ```
///
/// ## Complex Types with Serde
///
/// ```rust,no_run
/// use rink_core::hooks::storage::use_local_storage;
/// use serde::{Deserialize, Serialize};
///
/// #[derive(Clone, Serialize, Deserialize)]
/// struct UserPreferences {
///     theme: String,
///     language: String,
///     notifications: bool,
/// }
///
/// // In a component context:
/// let default_prefs = UserPreferences {
///     theme: "dark".to_string(),
///     language: "en".to_string(),
///     notifications: true,
/// };
///
/// let (prefs_handle, set_prefs) = use_local_storage("user_preferences".to_string(), default_prefs);
///
/// // Read preferences
/// let current_prefs = prefs_handle.get();
/// println!("Theme: {}", current_prefs.theme);
///
/// // Update preferences
/// set_prefs.update(|prev| UserPreferences {
///     theme: "light".to_string(),
///     ..prev.clone()
/// });
/// ```
///
/// ## Error Handling and Storage Operations
///
/// ```rust,no_run
/// use rink_core::hooks::storage::use_local_storage;
///
/// // In a component context:
/// let (data_handle, set_data) = use_local_storage("app_data".to_string(), vec![1, 2, 3]);
///
/// // Check if storage is available
/// if data_handle.is_storage_available() {
///     println!("Storage is available");
/// }
///
/// // Manual save operation with error handling
/// match set_data.save() {
///     Ok(()) => println!("Data saved successfully"),
///     Err(e) => eprintln!("Failed to save data: {}", e),
/// }
///
/// // Remove from storage but keep in-memory state
/// if let Err(e) = set_data.remove_from_storage() {
///     eprintln!("Failed to remove from storage: {}", e);
/// }
/// ```
///
/// ## Temporary Updates (Not Persisted)
///
/// ```rust,no_run
/// use rink_core::hooks::storage::use_local_storage;
///
/// // In a component context:
/// let (state_handle, set_state) = use_local_storage("temp_data".to_string(), "initial".to_string());
///
/// // Temporary update (not saved to storage)
/// set_state.set_temporary("temporary_value".to_string());
///
/// // Regular update (saved to storage)
/// set_state.set("persistent_value".to_string());
/// ```
///
/// # Error Handling
///
/// The hook is designed to be resilient to storage failures:
/// - If storage is unavailable, the hook works as a regular state hook
/// - Serialization/deserialization errors fall back to the default value
/// - Storage read/write errors don't break the reactive state
/// - All storage operations return `Result` types for explicit error handling
///
/// # Thread Safety
///
/// Both the handle and setter are thread-safe and can be safely shared across
/// async tasks and threads. The underlying storage operations are protected
/// by appropriate synchronization primitives.
///
/// # Performance Notes
///
/// - Storage operations are performed synchronously but are optimized for speed
/// - JSON serialization is used for type safety and human readability
/// - File I/O is minimized through intelligent caching strategies
/// - Memory usage is optimized through Arc-based sharing
///
/// # SSR Compatibility
///
/// The hook gracefully handles server-side rendering contexts where file storage
/// may not be available, falling back to in-memory state management.
/// Get or create a global state container for a storage key
fn get_or_create_storage_state<T>(
    key: &str,
    default_value: T,
) -> Arc<crate::hooks::state::StateContainer<T>>
where
    T: Clone + Serialize + for<'de> Deserialize<'de> + Send + Sync + 'static,
{
    let states_registry = STORAGE_STATES.get_or_init(|| RwLock::new(HashMap::new()));

    // First, try to get existing state
    {
        let states = states_registry.read();
        if let Some(existing_state) = states.get(key) {
            if let Some(typed_state) =
                existing_state.downcast_ref::<Arc<crate::hooks::state::StateContainer<T>>>()
            {
                return typed_state.clone();
            }
        }
    }

    // State doesn't exist, create new one
    let backend = get_storage_backend();
    let config = get_storage_config();

    // Try to load from storage first
    let initial_value = if backend.is_available() {
        match backend.read(key) {
            Ok(Some(json_str)) => {
                // Try to deserialize the stored value
                match serde_json::from_str::<T>(&json_str) {
                    Ok(stored_value) => stored_value,
                    Err(_) => {
                        // Deserialization failed, use default and save it
                        let json_str = if config.pretty_json {
                            serde_json::to_string_pretty(&default_value)
                        } else {
                            serde_json::to_string(&default_value)
                        };

                        if let Ok(json_str) = json_str {
                            let _ = backend.write(key, &json_str);
                        }

                        default_value
                    }
                }
            }
            _ => {
                // Storage read failed or no value found, use default
                default_value
            }
        }
    } else {
        // Storage not available, use default
        default_value
    };

    let new_container = Arc::new(crate::hooks::state::StateContainer::new(initial_value));

    // Store in global registry
    {
        let mut states = states_registry.write();
        states.insert(key.to_string(), Box::new(new_container.clone()));
    }

    new_container
}

pub fn use_local_storage<T>(
    key: String,
    default_value: T,
) -> (LocalStorageHandle<T>, LocalStorageSetter<T>)
where
    T: Clone + Serialize + for<'de> Deserialize<'de> + Send + Sync + 'static,
{
    // Get or create the global state container for this key
    let container = get_or_create_storage_state(&key, default_value);

    // Create the state handle and setter
    let state_handle = crate::hooks::state::StateHandle::from_container(container.clone());
    let state_setter = crate::hooks::state::StateSetter::new(container);

    // Create the local storage handle
    let backend = get_storage_backend();
    let config = get_storage_config();
    let storage_handle = LocalStorageHandle::new(state_handle, key.clone(), backend, config);

    // Create the local storage setter
    let storage_setter = LocalStorageSetter::new(storage_handle.clone(), state_setter);

    (storage_handle, storage_setter)
}
