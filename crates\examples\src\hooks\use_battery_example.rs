//! Professional use_battery Hook Example - Interactive TUI Demo
//!
//! This example demonstrates the use_battery hook that tracks battery status,
//! providing real-time information about battery level, charging status,
//! and time estimates similar to the web API's Battery Status API.

use crossterm::event::{Event, KeyCode};
use rink::prelude::*;
use rink_core::hooks::battery::{BatteryStatus, use_battery};

#[derive(Debu<PERSON>, <PERSON>lone, PartialEq)]
enum DisplayMode {
    Overview,
    Detailed,
    Historical,
    Alerts,
}

/// Interactive use_battery demonstration app
fn build_ui() -> impl Into<VNode> {
    let (app, controller) = use_app();

    // Application state
    let (display_mode, set_display_mode) = use_state(DisplayMode::Overview);
    let (alert_threshold, set_alert_threshold) = use_state(20.0f64); // Alert when below 20%
    let (update_count, set_update_count) = use_state(0i32);

    // Get battery status using the hook
    let battery = use_battery();

    // Track updates for demonstration
    let (previous_level, set_previous_level) = use_state(battery.level);
    if (battery.level - previous_level.get()).abs() > 0.001 {
        set_previous_level.set(battery.level);
        set_update_count.update(|c| c + 1);
    }

    // Get current values
    let current_mode = display_mode.get();
    let threshold = alert_threshold.get();
    let updates = update_count.get();

    // Handle keyboard events
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char('q') | KeyCode::Esc => {
                controller.exit(ExitReason::UserRequested);
            }
            KeyCode::Char('1') => {
                set_display_mode.set(DisplayMode::Overview);
            }
            KeyCode::Char('2') => {
                set_display_mode.set(DisplayMode::Detailed);
            }
            KeyCode::Char('3') => {
                set_display_mode.set(DisplayMode::Historical);
            }
            KeyCode::Char('4') => {
                set_display_mode.set(DisplayMode::Alerts);
            }
            KeyCode::Up | KeyCode::Char('+') => {
                set_alert_threshold.update(|current| (current + 5.0).min(95.0));
            }
            KeyCode::Down | KeyCode::Char('-') => {
                set_alert_threshold.update(|current| (current - 5.0).max(5.0));
            }
            KeyCode::Char('r') => {
                set_alert_threshold.set(20.0);
                set_update_count.set(0);
            }
            _ => {}
        }
    }

    // Handle app exit
    if app.should_exit() {
        return rsx! {
            <Block
                title="Goodbye!"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Red)}
            >
                <Paragraph
                    style={Style::default().fg(Color::White)}
                    alignment={Alignment::Center}
                >
                    {"Thanks for exploring use_battery!\n\n\
                     Key concepts demonstrated:\n\
                     • Real-time battery status monitoring\n\
                     • Cross-platform battery information\n\
                     • Automatic updates with configurable intervals\n\
                     • Battery level, charging status, and time estimates\n\
                     • Alert system for low battery conditions\n\n\
                     The use_battery hook provides comprehensive\n\
                     battery monitoring for TUI applications!"}
                </Paragraph>
            </Block>
        };
    }

    // Get app metrics
    let metrics = app.metrics();

    // Choose colors based on battery status
    let border_color = if !battery.is_supported {
        Color::Gray
    } else if battery.is_critical() {
        Color::Red
    } else if battery.is_low() {
        Color::Yellow
    } else if battery.charging {
        Color::Green
    } else {
        Color::Cyan
    };

    let (mode_description, content) = match current_mode {
        DisplayMode::Overview => {
            let level_bar = create_battery_bar(battery.level_percentage());
            (
                "Battery overview with visual indicator",
                format!(
                    "🔋 Battery Status Overview\n\n\
                    Support: {}\n\
                    Level: {:.1}% {}\n\
                    Status: {}\n\
                    \n\
                    Last Updated: {} seconds ago",
                    if battery.is_supported {
                        "✅ Supported"
                    } else {
                        "❌ Not Supported"
                    },
                    battery.level_percentage(),
                    level_bar,
                    battery.status_description(),
                    battery.last_updated.elapsed().unwrap_or_default().as_secs()
                ),
            )
        }
        DisplayMode::Detailed => (
            "Detailed battery information and timing",
            format!(
                "🔋 Detailed Battery Information\n\n\
                    Level: {:.1}% ({:.3} normalized)\n\
                    Charging: {}\n\
                    \n\
                    ⏱️ Time Estimates:\n\
                    Discharging: {}\n\
                    Charging: {}\n\
                    \n\
                    🔄 Update Info:\n\
                    Updates Received: {}\n\
                    Last Update: {:?}",
                battery.level_percentage(),
                battery.level,
                if battery.charging {
                    "🔌 Yes"
                } else {
                    "🔋 No"
                },
                battery.discharging_time_formatted(),
                battery.charging_time_formatted(),
                updates,
                battery.last_updated
            ),
        ),
        DisplayMode::Historical => (
            "Battery monitoring history and trends",
            format!(
                "📊 Battery Monitoring History\n\n\
                    Current Session:\n\
                    • Total Updates: {}\n\
                    • App Renders: {}\n\
                    • Events Processed: {}\n\
                    \n\
                    Battery Trends:\n\
                    • Current Level: {:.1}%\n\
                    • Status Changes: {} updates\n\
                    • Monitoring Active: ✅\n\
                    \n\
                    Performance:\n\
                    • Update Frequency: Every 5 seconds\n\
                    • Resource Usage: Minimal\n\
                    • Platform: Cross-platform",
                updates,
                metrics.render_count,
                metrics.event_count,
                battery.level_percentage(),
                updates
            ),
        ),
        DisplayMode::Alerts => {
            let is_below_threshold = battery.level_percentage() < threshold;
            let alert_status = if is_below_threshold {
                "🚨 ALERT TRIGGERED"
            } else {
                "✅ Normal"
            };

            (
                "Battery alert system and thresholds",
                format!(
                    "⚠️ Battery Alert System\n\n\
                    Alert Threshold: {:.0}%\n\
                    Current Level: {:.1}%\n\
                    Alert Status: {}\n\
                    \n\
                    Alert Conditions:\n\
                    • Critical (< 10%): {}\n\
                    • Low (< 20%): {}\n\
                    • Custom (< {:.0}%): {}\n\
                    \n\
                    Recommendations:\n\
                    {}",
                    threshold,
                    battery.level_percentage(),
                    alert_status,
                    if battery.is_critical() {
                        "🔴 Active"
                    } else {
                        "⚪ Inactive"
                    },
                    if battery.is_low() {
                        "🟡 Active"
                    } else {
                        "⚪ Inactive"
                    },
                    threshold,
                    if is_below_threshold {
                        "🟠 Active"
                    } else {
                        "⚪ Inactive"
                    },
                    get_battery_recommendations(&battery)
                ),
            )
        }
    };

    rsx! {
        <Block
            title="use_battery Hook Demo"
            borders={Borders::ALL}
            border_style={Style::default().fg(border_color)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Left}
            >
                {format!(
                    "🔋 Professional use_battery Hook Demonstration\n\n\
                    📊 Current State:\n\
                    Battery Level: {:.1}% | Charging: {} | Alert Threshold: {:.0}%\n\
                    Active Mode: {:?}\n\
                    Description: {}\n\n\
                    📋 Battery Information:\n\
                    {}\n\n\
                    🎮 Controls:\n\
                    1-4 : Switch display modes\n\
                    ↑/+ : Increase alert threshold\n\
                    ↓/- : Decrease alert threshold\n\
                    r   : Reset settings\n\
                    q   : Quit\n\n\
                    💡 Features:\n\
                    • Real-time battery monitoring\n\
                    • Cross-platform compatibility\n\
                    • Automatic status updates\n\
                    • Configurable alert thresholds\n\
                    • Time estimates for charging/discharging",
                    battery.level_percentage(),
                    if battery.charging { "Yes" } else { "No" },
                    threshold,
                    current_mode,
                    mode_description,
                    content
                )}
            </Paragraph>
        </Block>
    }
}

/// Create a visual battery level bar
fn create_battery_bar(percentage: f64) -> String {
    let filled_blocks = (percentage / 10.0) as usize;
    let total_blocks = 10;

    let mut bar = String::new();
    bar.push('[');

    for i in 0..total_blocks {
        if i < filled_blocks {
            bar.push('█');
        } else {
            bar.push('░');
        }
    }

    bar.push(']');
    bar
}

/// Get battery recommendations based on current status
fn get_battery_recommendations(battery: &BatteryStatus) -> String {
    if !battery.is_supported {
        return "Battery monitoring not available on this system".to_string();
    }

    if battery.is_critical() {
        "🔴 Critical: Connect charger immediately!".to_string()
    } else if battery.is_low() {
        "🟡 Low: Consider charging soon".to_string()
    } else if battery.charging && battery.level_percentage() > 80.0 {
        "🟢 Good: Battery is charging and at good level".to_string()
    } else if battery.charging {
        "🔵 Charging: Battery is being charged".to_string()
    } else {
        "✅ Normal: Battery level is adequate".to_string()
    }
}

/// Entry point of the application
fn main() -> Result<(), Box<dyn std::error::Error>> {
    render(build_ui)
}
