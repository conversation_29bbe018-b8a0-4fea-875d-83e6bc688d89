use std::time::Duration;

use crossterm::event::{Event, KeyCode, KeyEventKind};
use ratatui::{
    buffer::Buffer,
    layout::{Constraint, Direction, Layout, Rect},
    style::{Color, Modifier, Style},
    widgets::{Block, Borders, Paragraph},
};
use rink::{render, rsx, use_app, use_event, use_interval, use_state, VNode};
use tracing::{Level, info};

/// A React-like Counter component that mimics the Ink example
///
/// This component demonstrates:
/// - useState equivalent with use_state
/// - useEffect equivalent with use_interval
/// - Component composition with rsx!
/// - Automatic cleanup on unmount
fn counter() -> VNode {
    // useState equivalent - initialize counter to 0
    let (counter, set_counter) = rink::use_state(0);
    let counter_value = counter.get();

    // useEffect equivalent - setInterval that increments counter every 100ms
    use_interval(
        {
            let counter = counter.clone();
            move || {
                // setCounter(previousCounter => previousCounter + 1)
                set_counter(counter.get() + 1);
            }
        },
        Duration::from_millis(100), // 100ms interval like the React example
    );

    // Return JSX-like rsx! - equivalent to: <Text color="green">{counter} tests passed</Text>
    rsx! {
        <Paragraph
            style={Style::default().fg(Color::Green).add_modifier(Modifier::BOLD)}
            alignment={ratatui::layout::Alignment::Center}
        >
            {format!("{} tests passed", counter_value)}
        </Paragraph>
    }
}

/// A more elaborate version with multiple counters and styling
fn enhanced_counter() -> VNode {
    // Multiple state hooks - like multiple useState calls
    let (tests_passed, set_tests_passed) = use_state(0);
    let (tests_failed, set_tests_failed) = use_state(0);
    let (uptime_seconds, set_uptime_seconds) = use_state(0);

    // Get current values
    let tests_passed_value = tests_passed.get();
    let tests_failed_value = tests_failed.get();
    let uptime_seconds_value = uptime_seconds.get();

    // Fast counter for tests passed (every 100ms like React example)
    use_interval(
        {
            let tests_passed = tests_passed.clone();
            move || {
                set_tests_passed(tests_passed.get() + 1);
            }
        },
        Duration::from_millis(100),
    );

    // Slower counter for failed tests (every 500ms)
    use_interval(
        {
            let tests_failed = tests_failed.clone();
            move || {
                if tests_failed.get() < 5 {
                    set_tests_failed(tests_failed.get() + 1);
                }
            }
        },
        Duration::from_millis(500),
    );

    // Uptime counter (every second)
    use_interval(
        {
            let uptime_seconds = uptime_seconds.clone();
            move || {
                set_uptime_seconds(uptime_seconds.get() + 1);
            }
        },
        Duration::from_secs(1),
    );

    rsx! {
        <Layout
            direction={Direction::Vertical}
            constraints={vec![
                Constraint::Length(3),
                Constraint::Length(3),
                Constraint::Length(3),
                Constraint::Min(0),
            ]}
        >
            /* Tests Passed - Green like the original */
            <Block
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Green)}
                title="✅ Tests Passed"
            >
                <Paragraph
                    style={Style::default().fg(Color::Green).add_modifier(Modifier::BOLD)}
                    alignment={ratatui::layout::Alignment::Center}
                >
                    {format!("{} tests passed", tests_passed_value)}
                </Paragraph>
            </Block>

            /* Tests Failed - Red */
            <Block
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Red)}
                title="❌ Tests Failed"
            >
                <Paragraph
                    style={Style::default().fg(Color::Red).add_modifier(Modifier::BOLD)}
                    alignment={ratatui::layout::Alignment::Center}
                >
                    {format!("{} tests failed", tests_failed_value)}
                </Paragraph>
            </Block>

            /* Uptime - Blue */
            <Block
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Blue)}
                title="⏱️ Uptime"
            >
                <Paragraph
                    style={Style::default().fg(Color::Blue).add_modifier(Modifier::BOLD)}
                    alignment={ratatui::layout::Alignment::Center}
                >
                    {format!("{}s uptime", uptime_seconds_value)}
                </Paragraph>
            </Block>

            /* Instructions */
            <Block
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Yellow)}
                title="📝 Instructions"
            >
                <Paragraph
                    style={Style::default().fg(Color::Yellow)}
                    alignment={ratatui::layout::Alignment::Center}
                >
                    {"Press 'q' to quit • React-like hooks in Rust TUI"}
                </Paragraph>
            </Block>
        </Layout>
    }
}

fn react_like_counter(title: &str, enhanced_mode: bool) -> VNode {
    // Get app handle for exit control
    let (app, controller) = use_app();

    // Handle events (like event listeners in React)
    if let Some(Event::Key(key)) = use_event() {
        if key.kind == KeyEventKind::Press {
            match key.code {
                KeyCode::Char('q') => {
                    info!("User pressed 'q' - exiting React-like demo");
                    controller.shutdown();
                }
                KeyCode::Char('e') => {
                    info!("User pressed 'e' - toggling enhanced mode");
                    // In a real app, you'd use state for this
                }
                _ => {}
            }
        }
    }

    // Create the layout
    let layout = rsx! {
        <Layout
            direction={Direction::Vertical}
            margin={1}
            constraints={vec![
                Constraint::Length(3),  // Header
                Constraint::Min(0),     // Counter content
            ]}
        >
            /* Header */
            <Block
                title="🚀 React-like Counter in Rust TUI"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Cyan).add_modifier(Modifier::BOLD)}
            >
                <Paragraph
                    alignment={ratatui::layout::Alignment::Center}
                    style={Style::default().fg(Color::White).add_modifier(Modifier::BOLD)}
                >
                    {title.clone()}
                </Paragraph>
            </Block>

            /* Counter Component */
            {
                if enhanced_mode {
                    enhanced_counter()
                } else {
                    rsx! {
                        <Block
                            title="🧪 Test Runner"
                            borders={Borders::ALL}
                            border_style={Style::default().fg(Color::Green)}
                        >
                            <Counter />
                        </Block>
                    }
                }
            }
        </Layout>
    };
}

/// Entry point - equivalent to render(<Counter />) in React/Ink
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 Starting React-like Counter Demo...");
    println!("This mimics the React/Ink example with TUI framework hooks!");
    println!("Press 'q' to quit\n");

    // Choose which version to run
    let enhanced_mode = std::env::args().any(|arg| arg == "--enhanced");

    // Render the counter with the selected mode
    if let Err(err) = render(|| {
        react_like_counter(
            if enhanced_mode {
                "🎨 Enhanced Multi-Counter Demo"
            } else {
                "⚡ Simple Counter (React/Ink Style)"
            },
            enhanced_mode,
        )
    })
    .await
    {
        eprintln!("❌ Application error: {:?}", err);
    } else {
        println!("✨ React-like counter demo completed successfully!");
        println!("🎯 Demonstrated: useState → use_state, useEffect → use_interval");
    }

    Ok(())
}
