//! Professional use_callback Hook Example - Interactive TUI Demo
//!
//! This example demonstrates the use_callback hook with dependency tracking,
//! showing how it optimizes performance by memoizing callbacks and only
//! recreating them when dependencies change.

use crossterm::event::{Event, KeyCode};
use rink::prelude::*;
use rink_core::hooks::callback::{use_callback, use_callback_once, use_event_handler};

#[derive(Debug, Clone, PartialEq)]
enum DemoMode {
    BasicCallback,
    DependencyTracking,
    EventHandler,
    OnceCallback,
}

/// Interactive use_callback demonstration app
fn build_ui() -> impl Into<VNode> {
    let (app, controller) = use_app();

    // Application state
    let (counter, set_counter) = use_state(0i32);
    let (multiplier, set_multiplier) = use_state(2i32);
    let (demo_mode, set_demo_mode) = use_state(DemoMode::BasicCallback);
    let (callback_calls, set_callback_calls) = use_state(0i32);
    let (last_result, set_last_result) = use_state("No callback executed yet".to_string());

    // Get current values for dependency tracking
    let counter_value = counter.get();
    let multiplier_value = multiplier.get();
    let current_mode = demo_mode.get();

    // 1. Basic callback with dependencies - using ergonomic direct closure syntax
    let basic_callback = use_callback(
        {
            let set_last_result = set_last_result.clone();
            let set_callback_calls = set_callback_calls.clone();
            move |input: i32| {
                let result = input * multiplier_value;
                set_last_result.set(format!(
                    "Basic: {} * {} = {}",
                    input, multiplier_value, result
                ));
                set_callback_calls.update(|c| c + 1);
                result
            }
        },
        (counter_value, multiplier_value), // Dependencies
    );

    // 2. Callback that only depends on counter - using ergonomic direct closure syntax
    let counter_callback = use_callback(
        {
            let set_last_result = set_last_result.clone();
            let set_callback_calls = set_callback_calls.clone();
            move |input: i32| {
                let result = input + counter_value;
                set_last_result.set(format!(
                    "Counter: {} + {} = {}",
                    input, counter_value, result
                ));
                set_callback_calls.update(|c| c + 1);
                result
            }
        },
        counter_value, // Only counter dependency
    );

    // 3. Event handler with memoization
    let key_handler = use_event_handler(
        {
            let set_last_result = set_last_result.clone();
            let set_callback_calls = set_callback_calls.clone();
            move |key: KeyCode| {
                set_last_result.set(format!("Key pressed: {:?}", key));
                set_callback_calls.update(|c| c + 1);
            }
        },
        counter_value, // Recreate when counter changes
    );

    // 4. Once callback (never changes) - using ergonomic direct closure syntax
    let once_callback = use_callback_once({
        let set_last_result = set_last_result.clone();
        let set_callback_calls = set_callback_calls.clone();
        move |input: String| {
            set_last_result.set(format!("Once: {}", input));
            set_callback_calls.update(|c| c + 1);
        }
    });

    // Handle keyboard events
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char('q') | KeyCode::Esc => {
                controller.exit(ExitReason::UserRequested);
            }
            KeyCode::Char('1') => {
                set_demo_mode.set(DemoMode::BasicCallback);
                basic_callback.emit(5);
            }
            KeyCode::Char('2') => {
                set_demo_mode.set(DemoMode::DependencyTracking);
                counter_callback.emit(10);
            }
            KeyCode::Char('3') => {
                set_demo_mode.set(DemoMode::EventHandler);
                key_handler.emit(key.code);
            }
            KeyCode::Char('4') => {
                set_demo_mode.set(DemoMode::OnceCallback);
                once_callback.emit("Hello from once callback!".to_string());
            }
            KeyCode::Up | KeyCode::Char('+') => {
                set_counter.update(|current| current + 1);
            }
            KeyCode::Down | KeyCode::Char('-') => {
                set_counter.update(|current| current - 1);
            }
            KeyCode::Left => {
                set_multiplier.update(|current| (current - 1).max(1));
            }
            KeyCode::Right => {
                set_multiplier.update(|current| current + 1);
            }
            KeyCode::Char('r') => {
                set_counter.set(0);
                set_multiplier.set(2);
                set_callback_calls.set(0);
                set_last_result.set("Reset!".to_string());
            }
            _ => {}
        }
    }

    // Handle app exit
    if app.should_exit() {
        return rsx! {
            <Block
                title="Goodbye!"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Red)}
            >
                <Paragraph
                    style={Style::default().fg(Color::White)}
                    alignment={Alignment::Center}
                >
                    {"Thanks for exploring use_callback!\n\n\
                     Key concepts demonstrated:\n\
                     • Callback memoization with dependency tracking\n\
                     • Performance optimization through selective re-creation\n\
                     • Event handler optimization\n\
                     • Once callbacks for static behavior\n\
                     • Integration with React-like hooks pattern\n\n\
                     The use_callback hook helps prevent unnecessary\n\
                     re-renders and optimizes callback performance!"}
                </Paragraph>
            </Block>
        };
    }

    // Get current values for display
    let calls_count = callback_calls.get();
    let result_text = last_result.get();
    let metrics = app.metrics();

    // Choose colors based on demo mode
    let border_color = match current_mode {
        DemoMode::BasicCallback => Color::Cyan,
        DemoMode::DependencyTracking => Color::Green,
        DemoMode::EventHandler => Color::Yellow,
        DemoMode::OnceCallback => Color::Magenta,
    };

    let mode_description = match current_mode {
        DemoMode::BasicCallback => "Basic callback with multiple dependencies",
        DemoMode::DependencyTracking => "Callback with selective dependency tracking",
        DemoMode::EventHandler => "Event handler with memoization",
        DemoMode::OnceCallback => "Once callback (never recreated)",
    };

    rsx! {
        <Block
            title="use_callback Hook Demo"
            borders={Borders::ALL}
            border_style={Style::default().fg(border_color)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Left}
            >
                {format!(
                    "🔄 Professional use_callback Hook Demonstration 🔄\n\n\
                    📊 Current State:\n\
                    Counter: {} | Multiplier: {}\n\
                    Active Mode: {:?}\n\
                    Description: {}\n\n\
                    📋 Last Callback Result:\n\
                    {}\n\n\
                    📈 Performance Metrics:\n\
                    Total Callback Calls: {}\n\
                    App Renders: {} | Events: {}\n\n\
                    🎮 Controls:\n\
                    1-4 : Test different callback types\n\
                    ↑/+ : Increment counter (affects deps)\n\
                    ↓/- : Decrement counter (affects deps)\n\
                    ←/→ : Change multiplier (affects deps)\n\
                    r   : Reset all values\n\
                    q   : Quit\n\n\
                    💡 Key Concepts:\n\
                    • Callbacks are memoized based on dependencies\n\
                    • Only recreated when dependencies change\n\
                    • Optimizes performance in complex UIs\n\
                    • Prevents unnecessary re-renders\n\
                    • Follows React useCallback patterns",
                    counter_value,
                    multiplier_value,
                    current_mode,
                    mode_description,
                    result_text,
                    calls_count,
                    metrics.render_count,
                    metrics.event_count
                )}
            </Paragraph>
        </Block>
    }
}

/// Entry point of the application
fn main() -> Result<(), Box<dyn std::error::Error>> {
    render(build_ui)
}
