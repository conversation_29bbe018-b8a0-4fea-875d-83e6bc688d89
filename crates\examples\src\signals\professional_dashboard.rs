//! Professional Signal System Example - Real-time Dashboard
//!
//! This example demonstrates a comprehensive, production-ready TUI dashboard
//! that showcases all advanced signal features:
//! - Global signals for application state
//! - Computed signals for derived data
//! - Signal persistence for user preferences
//! - Signal middleware for logging and analytics
//! - Weak references for memory management
//!
//! The dashboard simulates a real-world monitoring application with:
//! - System metrics (CPU, Memory, Network)
//! - User preferences (theme, refresh rate)
//! - Real-time data updates
//! - Performance analytics
//! - Automatic state persistence

use rink::{prelude::List, render_async, rsx};
use rink_core::{
    VNode,
    hooks::{
        app::use_app,
        event::use_event,
        interval::use_interval,
        signal::{
            ComputedSignal, GlobalSignal, Signal,
            middleware::{AnalyticsMiddleware, CompositeMiddleware, LoggingMiddleware},
            persistence::{MemoryBackend, PersistenceBackend},
            weak_refs::{WeakSignalRef, WeakSignalRegistry},
        },
    },
};

use crossterm::event::{Event, KeyCode};
use ratatui::{
    layout::{Constraint, Direction},
    style::{Color, Style},
    text::{Line, Span},
    widgets::{Block, Borders, Gauge, ListItem, Paragraph, Wrap},
};
use std::{
    sync::Mutex,
    time::{Duration, SystemTime, UNIX_EPOCH},
};

// ============================================================================
// Global Application State with Signals
// ============================================================================

/// System metrics that update in real-time
#[derive(Debug, Clone)]
pub struct SystemMetrics {
    pub cpu_usage: f64,
    pub memory_usage: f64,
    pub network_rx: u64,
    pub network_tx: u64,
    pub disk_usage: f64,
    pub timestamp: u64,
}

impl Default for SystemMetrics {
    fn default() -> Self {
        Self {
            cpu_usage: 0.0,
            memory_usage: 0.0,
            network_rx: 0,
            network_tx: 0,
            disk_usage: 0.0,
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
        }
    }
}

/// User preferences that persist across sessions
#[derive(Debug, Clone)]
pub struct UserPreferences {
    pub theme: Theme,
    pub refresh_rate: u64, // milliseconds
    pub show_advanced_metrics: bool,
    pub alert_threshold: f64,
}

#[derive(Debug, Clone, PartialEq)]
pub enum Theme {
    Dark,
    Light,
    Blue,
    Green,
}

impl Default for UserPreferences {
    fn default() -> Self {
        Self {
            theme: Theme::Dark,
            refresh_rate: 1000,
            show_advanced_metrics: false,
            alert_threshold: 80.0,
        }
    }
}

/// Application status and metadata
#[derive(Debug, Clone)]
pub struct AppStatus {
    pub is_connected: bool,
    pub last_update: u64,
    pub error_count: u32,
    pub uptime: u64,
}

impl Default for AppStatus {
    fn default() -> Self {
        Self {
            is_connected: true,
            last_update: 0,
            error_count: 0,
            uptime: 0,
        }
    }
}

// ============================================================================
// Global Signals - The Heart of Our Application State
// ============================================================================

/// Current system metrics (updated by background tasks)
static SYSTEM_METRICS: GlobalSignal<SystemMetrics> = Signal::global(SystemMetrics::default);

/// User preferences (persisted to storage)
static USER_PREFERENCES: GlobalSignal<UserPreferences> = Signal::global(UserPreferences::default);

/// Application status and metadata
static APP_STATUS: GlobalSignal<AppStatus> = Signal::global(AppStatus::default);

/// Alert messages (dynamic list)
static ALERT_MESSAGES: GlobalSignal<Vec<String>> = Signal::global(Vec::new);

/// Application logs for the log area
static APP_LOGS: GlobalSignal<Vec<LogEntry>> = Signal::global(Vec::new);

/// Log entry structure
#[derive(Debug, Clone)]
pub struct LogEntry {
    pub timestamp: u64,
    pub level: LogLevel,
    pub message: String,
}

#[derive(Debug, Clone, PartialEq)]
pub enum LogLevel {
    Info,
    Warning,
    Error,
    Debug,
}

// ============================================================================
// Computed Signals - Derived State That Updates Automatically
// ============================================================================

/// Overall system health score (0-100) computed from all metrics
static SYSTEM_HEALTH_SCORE: ComputedSignal<f64> = ComputedSignal::new(|| {
    let metrics = SYSTEM_METRICS.get();
    let prefs = USER_PREFERENCES.get();

    // Calculate weighted health score
    let cpu_score = (100.0 - metrics.cpu_usage).max(0.0);
    let memory_score = (100.0 - metrics.memory_usage).max(0.0);
    let disk_score = (100.0 - metrics.disk_usage).max(0.0);

    // Apply user's alert threshold as a factor
    let threshold_factor = if metrics.cpu_usage > prefs.alert_threshold
        || metrics.memory_usage > prefs.alert_threshold
        || metrics.disk_usage > prefs.alert_threshold
    {
        0.7 // Reduce score if any metric exceeds threshold
    } else {
        1.0
    };

    ((cpu_score + memory_score + disk_score) / 3.0 * threshold_factor).round()
});

/// Status message based on current system state
static STATUS_MESSAGE: ComputedSignal<String> = ComputedSignal::new(|| {
    let health = SYSTEM_HEALTH_SCORE.get();
    let status = APP_STATUS.get();
    let alerts = ALERT_MESSAGES.get();

    if !status.is_connected {
        "🔴 DISCONNECTED - Unable to fetch metrics".to_string()
    } else if health < 30.0 {
        "🔴 CRITICAL - System performance severely degraded".to_string()
    } else if health < 60.0 {
        "🟡 WARNING - System performance degraded".to_string()
    } else if !alerts.is_empty() {
        format!("🟡 {} active alert(s)", alerts.len())
    } else {
        "🟢 HEALTHY - All systems operating normally".to_string()
    }
});

/// Theme colors computed from user preferences
static THEME_COLORS: ComputedSignal<ThemeColors> = ComputedSignal::new(|| {
    let theme = USER_PREFERENCES.get().theme;
    match theme {
        Theme::Dark => ThemeColors {
            primary: Color::Cyan,
            secondary: Color::Gray,
            success: Color::Green,
            warning: Color::Yellow,
            error: Color::Red,
            background: Color::Black,
        },
        Theme::Light => ThemeColors {
            primary: Color::Blue,
            secondary: Color::DarkGray,
            success: Color::Green,
            warning: Color::Rgb(255, 165, 0), // Orange
            error: Color::Red,
            background: Color::White,
        },
        Theme::Blue => ThemeColors {
            primary: Color::Blue,
            secondary: Color::Cyan,
            success: Color::Green,
            warning: Color::Yellow,
            error: Color::Red,
            background: Color::Black,
        },
        Theme::Green => ThemeColors {
            primary: Color::Green,
            secondary: Color::DarkGray,
            success: Color::Green,
            warning: Color::Yellow,
            error: Color::Red,
            background: Color::Black,
        },
    }
});

#[derive(Debug, Clone)]
pub struct ThemeColors {
    pub primary: Color,
    pub secondary: Color,
    pub success: Color,
    pub warning: Color,
    pub error: Color,
    pub background: Color,
}

// ============================================================================
// Professional Middleware Setup
// ============================================================================

/// Global middleware for signal monitoring and analytics
static SIGNAL_MIDDLEWARE: std::sync::OnceLock<CompositeMiddleware<SystemMetrics>> =
    std::sync::OnceLock::new();

/// Initialize professional middleware stack
fn initialize_middleware() -> CompositeMiddleware<SystemMetrics> {
    CompositeMiddleware::new()
        .add(LoggingMiddleware) // Debug logging in development
        .add(AnalyticsMiddleware::new()) // Usage analytics
}

// ============================================================================
// Persistence Layer
// ============================================================================

/// Global persistence backend for user preferences
static PERSISTENCE_BACKEND: std::sync::OnceLock<MemoryBackend> = std::sync::OnceLock::new();

/// Save user preferences to persistent storage
fn save_preferences() {
    let backend = PERSISTENCE_BACKEND.get_or_init(MemoryBackend::default);
    let prefs = USER_PREFERENCES.get();

    // In a real application, you'd serialize to JSON or another format
    let serialized = format!("{:?}", prefs);
    if let Err(e) = backend.save("user_preferences", &serialized) {
        eprintln!("Failed to save preferences: {:?}", e);
    }
}

/// Load user preferences from persistent storage
fn load_preferences() {
    let backend = PERSISTENCE_BACKEND.get_or_init(MemoryBackend::default);

    if let Ok(Some(_serialized)) = backend.load("user_preferences") {
        // In a real application, you'd deserialize from JSON
        // For this example, we'll use defaults
        log_message(LogLevel::Info, "Loaded user preferences from storage");
    } else {
        log_message(LogLevel::Info, "Using default user preferences");
    }
}

// ============================================================================
// Weak Reference Management
// ============================================================================

/// Registry for managing weak references to prevent memory leaks
static WEAK_REGISTRY: std::sync::OnceLock<Mutex<WeakSignalRegistry<SystemMetrics>>> =
    std::sync::OnceLock::new();

/// Register a weak reference for memory management
fn register_weak_reference() {
    let registry = WEAK_REGISTRY.get_or_init(|| Mutex::new(WeakSignalRegistry::new()));
    let weak_ref = WeakSignalRef::from_global(&SYSTEM_METRICS);

    if let Ok(reg) = registry.lock() {
        reg.add(weak_ref);

        // Periodic cleanup of dead references
        let live_count = reg.cleanup_dead_refs();
        log_message(
            LogLevel::Debug,
            &format!("Weak registry: {} live references", live_count),
        );
    }
}

/// Add a log entry to the application logs
fn log_message(level: LogLevel, message: &str) {
    let entry = LogEntry {
        timestamp: SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs(),
        level,
        message: message.to_string(),
    };

    APP_LOGS.update(|mut logs| {
        logs.push(entry);
        // Keep only the last 100 log entries to prevent memory growth
        if logs.len() > 100 {
            logs.drain(0..logs.len() - 100);
        }
        logs
    });
}

// ============================================================================
// Main Application Component
// ============================================================================

/// Professional dashboard application entry point
pub async fn run_professional_dashboard() -> Result<(), std::boxed::Box<dyn std::error::Error>> {
    // Initialize application systems
    initialize_application();

    // Main application logic
    render_async(build_dashboard_ui_async).await
}

/// Initialize all application systems and load persisted state
fn initialize_application() {
    // Initialize middleware
    let _middleware = SIGNAL_MIDDLEWARE.get_or_init(initialize_middleware);
    log_message(LogLevel::Info, "Signal middleware initialized");

    // Load persisted preferences
    load_preferences();
    log_message(LogLevel::Info, "User preferences loaded");

    // Register weak references for memory management
    register_weak_reference();

    // Initialize app status
    APP_STATUS.update(|mut status| {
        status.uptime = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        status
    });
    log_message(
        LogLevel::Info,
        "Application systems initialized successfully",
    );
}

/// Main dashboard UI builder with async rendering support
fn build_dashboard_ui_async() -> VNode {
    // Application lifecycle management
    let (app, app_controller) = use_app();

    // Check for application exit
    if app.should_exit() {
        return render_shutdown_screen();
    }

    // Handle real-time data updates
    setup_data_simulation();

    // Handle user input
    handle_user_input(&app_controller);

    // Render the main dashboard
    render_main_dashboard()
}

/// Simulate real-time data updates (in a real app, this would be actual system monitoring)
fn setup_data_simulation() {
    use_interval(
        || {
            // Simulate system metrics updates
            SYSTEM_METRICS.update(|mut metrics| {
                metrics.cpu_usage =
                    (metrics.cpu_usage + (rand::random::<f64>() - 0.5) * 10.0).clamp(0.0, 100.0);
                metrics.memory_usage =
                    (metrics.memory_usage + (rand::random::<f64>() - 0.5) * 5.0).clamp(0.0, 100.0);
                metrics.disk_usage =
                    (metrics.disk_usage + (rand::random::<f64>() - 0.5) * 2.0).clamp(0.0, 100.0);
                metrics.network_rx += rand::random::<u64>() % 1000;
                metrics.network_tx += rand::random::<u64>() % 800;
                metrics.timestamp = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs();
                metrics
            });

            // Update app status
            APP_STATUS.update(|mut status| {
                status.last_update = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs();
                status
            });

            // Check for alerts based on thresholds
            check_and_update_alerts();
        },
        Duration::from_millis(USER_PREFERENCES.get().refresh_rate),
    );
}

/// Handle user keyboard input
fn handle_user_input(app_controller: &rink_core::hooks::app::AppController) {
    if let Some(Event::Key(key)) = use_event() {
        if !key.is_press() {
            return;
        }

        match key.code {
            KeyCode::Char('q') | KeyCode::Esc => {
                // Save preferences before exit
                save_preferences();
                app_controller.shutdown();
            }
            KeyCode::Char('t') => {
                // Cycle through themes
                cycle_theme();
            }
            KeyCode::Char('r') => {
                // Toggle refresh rate
                toggle_refresh_rate();
            }
            KeyCode::Char('a') => {
                // Toggle advanced metrics
                toggle_advanced_metrics();
            }
            KeyCode::Char('c') => {
                // Clear alerts
                ALERT_MESSAGES.set(Vec::new());
            }
            KeyCode::Char('h') => {
                // Show help
                show_help();
            }
            _ => {}
        }
    }
}

/// Check system metrics against thresholds and update alerts
fn check_and_update_alerts() {
    let metrics = SYSTEM_METRICS.get();
    let prefs = USER_PREFERENCES.get();
    let mut alerts = ALERT_MESSAGES.get();

    // Clear old alerts
    alerts.clear();

    // Check CPU threshold
    if metrics.cpu_usage > prefs.alert_threshold {
        alerts.push(format!("High CPU usage: {:.1}%", metrics.cpu_usage));
    }

    // Check memory threshold
    if metrics.memory_usage > prefs.alert_threshold {
        alerts.push(format!("High memory usage: {:.1}%", metrics.memory_usage));
    }

    // Check disk threshold
    if metrics.disk_usage > prefs.alert_threshold {
        alerts.push(format!("High disk usage: {:.1}%", metrics.disk_usage));
    }

    // Update alerts if changed
    if alerts != ALERT_MESSAGES.get() {
        ALERT_MESSAGES.set(alerts);
    }
}

/// Cycle through available themes
fn cycle_theme() {
    USER_PREFERENCES.update(|mut prefs| {
        prefs.theme = match prefs.theme {
            Theme::Dark => Theme::Light,
            Theme::Light => Theme::Blue,
            Theme::Blue => Theme::Green,
            Theme::Green => Theme::Dark,
        };
        prefs
    });
    let new_theme = USER_PREFERENCES.get().theme;
    log_message(
        LogLevel::Info,
        &format!("Theme changed to: {:?}", new_theme),
    );
    save_preferences();
}

/// Toggle between fast and slow refresh rates
fn toggle_refresh_rate() {
    USER_PREFERENCES.update(|mut prefs| {
        prefs.refresh_rate = if prefs.refresh_rate == 1000 {
            500
        } else {
            1000
        };
        prefs
    });
    let new_rate = USER_PREFERENCES.get().refresh_rate;
    log_message(
        LogLevel::Info,
        &format!("Refresh rate changed to: {}ms", new_rate),
    );
    save_preferences();
}

/// Toggle advanced metrics display
fn toggle_advanced_metrics() {
    USER_PREFERENCES.update(|mut prefs| {
        prefs.show_advanced_metrics = !prefs.show_advanced_metrics;
        prefs
    });
    save_preferences();
}

/// Show help information
fn show_help() {
    ALERT_MESSAGES.update(|mut alerts| {
        alerts.clear();
        alerts.push(
            "HELP: Press 't' for theme, 'r' for refresh rate, 'a' for advanced metrics".to_string(),
        );
        alerts.push("      Press 'c' to clear alerts, 'h' for help, 'q' to quit".to_string());
        alerts
    });
}

// ============================================================================
// UI Rendering Components
// ============================================================================

/// Render the main dashboard layout
fn render_main_dashboard() -> VNode {
    let colors = THEME_COLORS.get();

    rsx! {
        <Layout direction={Direction::Vertical} constraints={[
            Constraint::Length(3),  // Header
            Constraint::Min(10),    // Main content
            Constraint::Length(3),  // Footer
        ]}>
            {render_header(&colors)}
            {render_main_content(&colors)}
            {render_footer(&colors)}
        </Layout>
    }
}

/// Render the application header with title and status
fn render_header(colors: &ThemeColors) -> VNode {
    let status_msg = STATUS_MESSAGE.get();
    let health_score = SYSTEM_HEALTH_SCORE.get();

    rsx! {
        <Block
            title="🖥️  Professional System Dashboard"
            borders={Borders::ALL}
            border_style={Style::default().fg(colors.primary)}
        >
            <Paragraph
                style={Style::default().fg(colors.secondary)}
                wrap={Wrap { trim: true }}
            >
                {format!(
                    "Health Score: {:.0}/100 | Status: {} | Theme: {:?}",
                    health_score,
                    status_msg,
                    USER_PREFERENCES.get().theme
                )}
            </Paragraph>
        </Block>
    }
}

/// Render the main content area with metrics, alerts, and logs
fn render_main_content(colors: &ThemeColors) -> VNode {
    rsx! {
        <Layout direction={Direction::Horizontal} constraints={[
            Constraint::Percentage(50), // Metrics panel
            Constraint::Percentage(25), // Alerts panel
            Constraint::Percentage(25), // Logs panel
        ]}>
            {render_metrics_panel(colors)}
            {render_alerts_panel(colors)}
            {render_logs_panel(colors)}
        </Layout>
    }
}

/// Render the system metrics panel
fn render_metrics_panel(colors: &ThemeColors) -> VNode {
    let metrics = SYSTEM_METRICS.get();
    let prefs = USER_PREFERENCES.get();

    rsx! {
        <Layout direction={Direction::Vertical} constraints={[
            Constraint::Length(3), // CPU
            Constraint::Length(3), // Memory
            Constraint::Length(3), // Disk
            Constraint::Min(4),    // Network (if advanced)
        ]}>
            {render_metric_gauge("CPU Usage", metrics.cpu_usage, colors, prefs.alert_threshold)}
            {render_metric_gauge("Memory Usage", metrics.memory_usage, colors, prefs.alert_threshold)}
            {render_metric_gauge("Disk Usage", metrics.disk_usage, colors, prefs.alert_threshold)}
            {if prefs.show_advanced_metrics {
                render_network_metrics(&metrics, colors)
            } else {
                render_basic_info(colors)
            }}
        </Layout>
    }
}

/// Render a metric gauge with color coding based on threshold
fn render_metric_gauge(title: &str, value: f64, colors: &ThemeColors, threshold: f64) -> VNode {
    let gauge_color = if value > threshold {
        colors.error
    } else if value > threshold * 0.7 {
        colors.warning
    } else {
        colors.success
    };

    rsx! {
        <Block
            title={title.to_string()}
            borders={Borders::ALL}
            border_style={Style::default().fg(colors.secondary)}
        >
            <Gauge
                block={Block::default()}
                gauge_style={Style::default().fg(gauge_color)}
                percent={(value as u16).min(100)}
                label={format!("{:.1}%", value)}
            />
        </Block>
    }
}

/// Render network metrics (advanced view)
fn render_network_metrics(metrics: &SystemMetrics, colors: &ThemeColors) -> VNode {
    rsx! {
        <Block
            title="Network Activity"
            borders={Borders::ALL}
            border_style={Style::default().fg(colors.secondary)}
        >
            <Paragraph
                style={Style::default().fg(colors.primary)}
                wrap={Wrap { trim: true }}
            >
                {format!(
                    "📡 RX: {} KB/s\n📤 TX: {} KB/s\n🕒 Last Update: {}",
                    metrics.network_rx / 1024,
                    metrics.network_tx / 1024,
                    format_timestamp(metrics.timestamp)
                )}
            </Paragraph>
        </Block>
    }
}

/// Render basic system information
fn render_basic_info(colors: &ThemeColors) -> VNode {
    let status = APP_STATUS.get();
    let prefs = USER_PREFERENCES.get();

    rsx! {
        <Block
            title="System Info"
            borders={Borders::ALL}
            border_style={Style::default().fg(colors.secondary)}
        >
            <Paragraph
                style={Style::default().fg(colors.primary)}
                wrap={Wrap { trim: true }}
            >
                {format!(
                    "⚡ Refresh Rate: {}ms\n🎨 Theme: {:?}\n⏱️  Uptime: {}s\n🔗 Connected: {}",
                    prefs.refresh_rate,
                    prefs.theme,
                    status.uptime,
                    if status.is_connected { "Yes" } else { "No" }
                )}
            </Paragraph>
        </Block>
    }
}

/// Render the alerts and notifications panel (more compact)
fn render_alerts_panel(colors: &ThemeColors) -> VNode {
    let alerts = ALERT_MESSAGES.get();

    rsx! {
        <Layout direction={Direction::Vertical} constraints={[
            Constraint::Min(3),     // Alerts list
            Constraint::Length(6),  // Controls help (compact)
        ]}>
            {render_alerts_list(&alerts, colors)}
            {render_controls_help_compact(colors)}
        </Layout>
    }
}

/// Render the list of active alerts
fn render_alerts_list(alerts: &[String], colors: &ThemeColors) -> VNode {
    // Create the list items as VNodes
    let alert_vnodes: Vec<ListItem> = alerts
        .iter()
        .map(|alert| {
            ListItem::new(Line::from(vec![
                Span::styled("⚠️ ".to_string(), Style::default().fg(colors.warning)),
                Span::styled(alert.clone(), Style::default().fg(colors.primary)),
            ]))
        })
        .collect();

    rsx! {
        <Block
            title={format!("Alerts ({})", alerts.len())}
            borders={Borders::ALL}
            border_style={Style::default().fg(if alerts.is_empty() { colors.success } else { colors.warning })}
        >
            <List style={Style::default().fg(colors.secondary)}>
                {alert_vnodes}
            </List>
        </Block>
    }
}

/// Render the compact controls help panel
fn render_controls_help_compact(colors: &ThemeColors) -> VNode {
    rsx! {
        <Block
            title="Controls"
            borders={Borders::ALL}
            border_style={Style::default().fg(colors.secondary)}
        >
            <Paragraph
                style={Style::default().fg(colors.primary)}
                wrap={Wrap { trim: true }}
            >
                {"t=theme r=refresh a=advanced\nc=clear h=help q=quit"}
            </Paragraph>
        </Block>
    }
}

/// Render the application logs panel
fn render_logs_panel(colors: &ThemeColors) -> VNode {
    let logs = APP_LOGS.get();

    // Take the last 10 log entries for display and clone them to avoid lifetime issues
    let recent_logs: Vec<LogEntry> = logs.iter().rev().take(10).cloned().collect();

    rsx! {
        <Block
            title={format!("Logs ({})", logs.len())}
            borders={Borders::ALL}
            border_style={Style::default().fg(colors.secondary)}
        >
            <List style={Style::default().fg(colors.secondary)}>
                {recent_logs
                    .iter()
                    .map(|log| {
                        let level_icon = match log.level {
                            LogLevel::Info => "ℹ️",
                            LogLevel::Warning => "⚠️",
                            LogLevel::Error => "❌",
                            LogLevel::Debug => "🔍",
                        };

                        let level_color = match log.level {
                            LogLevel::Info => colors.primary,
                            LogLevel::Warning => colors.warning,
                            LogLevel::Error => colors.error,
                            LogLevel::Debug => colors.secondary,
                        };

                        ListItem::new(Line::from(vec![
                            Span::styled(level_icon.to_string(), Style::default().fg(level_color)),
                            Span::styled(" ".to_string(), Style::default()),
                            Span::styled(log.message.clone(), Style::default().fg(colors.primary)),
                        ]))
                    })
                    .collect::<Vec<_>>()
                }
            </List>
        </Block>
    }
}

/// Render the footer with performance stats
fn render_footer(colors: &ThemeColors) -> VNode {
    let app_metrics = use_app().0.metrics();
    let health = SYSTEM_HEALTH_SCORE.get();

    rsx! {
        <Block
            title="Performance"
            borders={Borders::ALL}
            border_style={Style::default().fg(colors.secondary)}
        >
            <Paragraph
                style={Style::default().fg(colors.primary)}
                wrap={Wrap { trim: true }}
            >
                {format!(
                    "🚀 Renders: {} | ⚡ Avg Frame: {:.1}ms | 💚 Health: {:.0}% | 🎯 Signals: Active",
                    app_metrics.render_count,
                    app_metrics.avg_frame_time_ms,
                    health
                )}
            </Paragraph>
        </Block>
    }
}

/// Render shutdown screen
fn render_shutdown_screen() -> VNode {
    rsx! {
        <Block
            title="Shutting Down"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Red)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                wrap={Wrap { trim: true }}
            >
                {"🔄 Professional Dashboard is shutting down gracefully...\n\n\
                 ✅ Saving user preferences\n\
                 ✅ Cleaning up signal middleware\n\
                 ✅ Releasing weak references\n\
                 ✅ Finalizing persistence layer\n\n\
                 Thank you for using the Professional Signal Dashboard!"}
            </Paragraph>
        </Block>
    }
}

// ============================================================================
// Utility Functions
// ============================================================================

/// Format timestamp for display
fn format_timestamp(timestamp: u64) -> String {
    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs();
    let diff = now.saturating_sub(timestamp);

    if diff < 60 {
        format!("{}s ago", diff)
    } else if diff < 3600 {
        format!("{}m ago", diff / 60)
    } else {
        format!("{}h ago", diff / 3600)
    }
}

// ============================================================================
// Main Function for Binary Execution
// ============================================================================

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    tracing_subscriber::fmt::init();

    // Initialize the signal system and demonstrate features
    initialize_signal_features().await;

    // Run the professional dashboard
    run_professional_dashboard().await
}

/// Initialize and demonstrate the signal system features using the log area
async fn initialize_signal_features() {
    log_message(LogLevel::Info, "� Starting Professional Signal Dashboard");
    log_message(LogLevel::Info, "📊 Initializing advanced signal system");

    // 1. Global Signal Updates
    log_message(LogLevel::Info, "Updating global signals");
    SYSTEM_METRICS.update(|mut metrics| {
        metrics.cpu_usage = 45.2;
        metrics.memory_usage = 67.8;
        metrics.disk_usage = 23.1;
        metrics
    });

    // 2. Computed Signal Reactivity
    let health_score = SYSTEM_HEALTH_SCORE.get();
    let status = STATUS_MESSAGE.get();
    log_message(
        LogLevel::Info,
        &format!("Health Score: {:.0}/100", health_score),
    );
    log_message(LogLevel::Info, &format!("Status: {}", status));

    // 3. User Preferences
    log_message(LogLevel::Info, "Updating user preferences");
    USER_PREFERENCES.update(|mut prefs| {
        prefs.theme = Theme::Blue;
        prefs.refresh_rate = 500;
        prefs.alert_threshold = 75.0;
        prefs
    });

    // 4. Theme Colors (computed from preferences)
    let colors = THEME_COLORS.get();
    log_message(
        LogLevel::Info,
        &format!("Theme colors updated: {:?}", colors.primary),
    );

    // 5. Persistence
    log_message(LogLevel::Info, "Saving preferences to storage");
    save_preferences();

    // 6. Middleware Analytics
    log_message(LogLevel::Info, "Signal middleware is tracking all changes");

    // 7. Weak References
    log_message(LogLevel::Info, "Weak reference management active");
    register_weak_reference();

    // 8. Alert System
    log_message(LogLevel::Warning, "Testing alert system");
    SYSTEM_METRICS.update(|mut metrics| {
        metrics.cpu_usage = 85.0; // Trigger alert
        metrics
    });

    // Wait a moment for computed signals to update
    tokio::time::sleep(Duration::from_millis(100)).await;

    let alerts = ALERT_MESSAGES.get();
    if !alerts.is_empty() {
        log_message(
            LogLevel::Warning,
            &format!("Alerts triggered: {}", alerts.len()),
        );
    }

    log_message(
        LogLevel::Info,
        "✅ All signal features initialized successfully",
    );
    log_message(LogLevel::Info, "🎯 Professional Signal Dashboard ready");
}
