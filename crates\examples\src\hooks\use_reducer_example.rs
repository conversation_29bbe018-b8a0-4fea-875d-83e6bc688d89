//! Comprehensive use_reducer hook examples
//!
//! This module demonstrates various patterns for the use_reducer hook,
//! including simple counters, complex state management, and real-world scenarios.

use crossterm::event::{Event, KeyCode};
use rink::prelude::*;
use rink_core::hooks::reducer::use_reducer;

/// Simple counter example with reducer
#[derive(Clone, Debug)]
pub enum CounterAction {
    Increment,
    Decrement,
    Reset,
    SetValue(i32),
    Add(i32),
}

pub fn counter_reducer(state: i32, action: CounterAction) -> i32 {
    match action {
        CounterAction::Increment => state + 1,
        CounterAction::Decrement => state - 1,
        CounterAction::Reset => 0,
        CounterAction::SetValue(value) => value,
        CounterAction::Add(value) => state + value,
    }
}

pub fn simple_counter_example() -> impl Into<VNode> {
    let (state, dispatch) = use_reducer(counter_reducer, 0);
    let count = state.get();

    // Handle keyboard events
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char('+') | KeyCode::Char('=') => {
                dispatch.call(CounterAction::Increment);
            }
            KeyCode::Char('-') => {
                dispatch.call(CounterAction::Decrement);
            }
            KeyCode::Char('r') => {
                dispatch.call(CounterAction::Reset);
            }
            KeyCode::Char('5') => {
                dispatch.call(CounterAction::Add(5));
            }
            KeyCode::Char('1') => {
                dispatch.call(CounterAction::SetValue(100));
            }
            _ => {}
        }
    }

    rsx! {
        <Block
            title="Simple Counter (use_reducer)"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Green)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Center}
            >
                {format!(
                    "Count: {}\n\nControls:\n+ or = : Increment\n- : Decrement\nr : Reset\n5 : Add 5\n1 : Set to 100",
                    count
                )}
            </Paragraph>
        </Block>
    }
}

/// Todo list example with complex state management
#[derive(Clone, Debug)]
pub struct TodoState {
    pub todos: Vec<Todo>,
    pub filter: Filter,
    pub next_id: u32,
    pub stats: TodoStats,
}

#[derive(Clone, Debug)]
pub struct Todo {
    pub id: u32,
    pub text: String,
    pub completed: bool,
    pub priority: Priority,
}

#[derive(Clone, Debug, PartialEq)]
pub enum Filter {
    All,
    Active,
    Completed,
    HighPriority,
}

#[derive(Clone, Debug, PartialEq)]
pub enum Priority {
    Low,
    Medium,
    High,
}

#[derive(Clone, Debug)]
pub struct TodoStats {
    pub total: usize,
    pub completed: usize,
    pub active: usize,
    pub high_priority: usize,
}

#[derive(Clone, Debug)]
pub enum TodoAction {
    AddTodo(String, Priority),
    ToggleTodo(u32),
    RemoveTodo(u32),
    SetFilter(Filter),
    ClearCompleted,
    SetPriority(u32, Priority),
    EditTodo(u32, String),
}

pub fn todo_reducer(state: TodoState, action: TodoAction) -> TodoState {
    let mut new_state = match action {
        TodoAction::AddTodo(text, priority) => {
            let mut todos = state.todos;
            todos.push(Todo {
                id: state.next_id,
                text,
                completed: false,
                priority,
            });
            TodoState {
                todos,
                next_id: state.next_id + 1,
                ..state
            }
        }
        TodoAction::ToggleTodo(id) => {
            let todos = state
                .todos
                .into_iter()
                .map(|mut todo| {
                    if todo.id == id {
                        todo.completed = !todo.completed;
                    }
                    todo
                })
                .collect();
            TodoState { todos, ..state }
        }
        TodoAction::RemoveTodo(id) => {
            let todos = state
                .todos
                .into_iter()
                .filter(|todo| todo.id != id)
                .collect();
            TodoState { todos, ..state }
        }
        TodoAction::SetFilter(filter) => TodoState { filter, ..state },
        TodoAction::ClearCompleted => {
            let todos = state
                .todos
                .into_iter()
                .filter(|todo| !todo.completed)
                .collect();
            TodoState { todos, ..state }
        }
        TodoAction::SetPriority(id, priority) => {
            let todos = state
                .todos
                .into_iter()
                .map(|mut todo| {
                    if todo.id == id {
                        todo.priority = priority.clone();
                    }
                    todo
                })
                .collect();
            TodoState { todos, ..state }
        }
        TodoAction::EditTodo(id, new_text) => {
            let todos = state
                .todos
                .into_iter()
                .map(|mut todo| {
                    if todo.id == id {
                        todo.text = new_text.clone();
                    }
                    todo
                })
                .collect();
            TodoState { todos, ..state }
        }
    };

    // Update stats
    new_state.stats = TodoStats {
        total: new_state.todos.len(),
        completed: new_state.todos.iter().filter(|t| t.completed).count(),
        active: new_state.todos.iter().filter(|t| !t.completed).count(),
        high_priority: new_state
            .todos
            .iter()
            .filter(|t| matches!(t.priority, Priority::High))
            .count(),
    };

    new_state
}

pub fn todo_list_example() -> impl Into<VNode> {
    let initial_state = TodoState {
        todos: vec![
            Todo {
                id: 1,
                text: "Learn Rust".to_string(),
                completed: false,
                priority: Priority::High,
            },
            Todo {
                id: 2,
                text: "Build TUI app".to_string(),
                completed: true,
                priority: Priority::Medium,
            },
        ],
        filter: Filter::All,
        next_id: 3,
        stats: TodoStats {
            total: 2,
            completed: 1,
            active: 1,
            high_priority: 1,
        },
    };

    let (state, dispatch) = use_reducer(todo_reducer, initial_state);
    let current_state = state.get();

    // Handle keyboard events
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char('a') => {
                dispatch.call(TodoAction::AddTodo(
                    "New Task".to_string(),
                    Priority::Medium,
                ));
            }
            KeyCode::Char('h') => {
                dispatch.call(TodoAction::AddTodo(
                    "High Priority Task".to_string(),
                    Priority::High,
                ));
            }
            KeyCode::Char('1') => {
                if !current_state.todos.is_empty() {
                    dispatch.call(TodoAction::ToggleTodo(current_state.todos[0].id));
                }
            }
            KeyCode::Char('2') => {
                if current_state.todos.len() > 1 {
                    dispatch.call(TodoAction::ToggleTodo(current_state.todos[1].id));
                }
            }
            KeyCode::Char('d') => {
                if !current_state.todos.is_empty() {
                    dispatch.call(TodoAction::RemoveTodo(current_state.todos[0].id));
                }
            }
            KeyCode::Char('f') => {
                let next_filter = match current_state.filter {
                    Filter::All => Filter::Active,
                    Filter::Active => Filter::Completed,
                    Filter::Completed => Filter::HighPriority,
                    Filter::HighPriority => Filter::All,
                };
                dispatch.call(TodoAction::SetFilter(next_filter));
            }
            KeyCode::Char('c') => {
                dispatch.call(TodoAction::ClearCompleted);
            }
            _ => {}
        }
    }

    // Filter todos based on current filter
    let filtered_todos: Vec<&Todo> = current_state
        .todos
        .iter()
        .filter(|todo| match current_state.filter {
            Filter::All => true,
            Filter::Active => !todo.completed,
            Filter::Completed => todo.completed,
            Filter::HighPriority => matches!(todo.priority, Priority::High),
        })
        .collect();

    let todo_list = if filtered_todos.is_empty() {
        "No todos match current filter".to_string()
    } else {
        filtered_todos
            .iter()
            .enumerate()
            .map(|(i, todo)| {
                let status = if todo.completed { "✓" } else { "○" };
                let priority = match todo.priority {
                    Priority::High => "🔴",
                    Priority::Medium => "🟡",
                    Priority::Low => "🟢",
                };
                format!("{}. {} {} {}", i + 1, status, priority, todo.text)
            })
            .collect::<Vec<_>>()
            .join("\n")
    };

    rsx! {
        <Block
            title={format!("Todo List - Filter: {:?} (use_reducer)", current_state.filter)}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Blue)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Left}
            >
                {format!(
                    "Stats: {} total, {} active, {} completed, {} high priority\n\nTodos:\n{}\n\nControls:\na : Add task\nh : Add high priority\n1/2 : Toggle task\nd : Delete first\nf : Change filter\nc : Clear completed",
                    current_state.stats.total,
                    current_state.stats.active,
                    current_state.stats.completed,
                    current_state.stats.high_priority,
                    todo_list
                )}
            </Paragraph>
        </Block>
    }
}

/// Shopping cart example with complex business logic
#[derive(Clone, Debug)]
pub struct CartState {
    pub items: Vec<CartItem>,
    pub total: f64,
    pub discount: f64,
    pub tax_rate: f64,
    pub final_total: f64,
}

#[derive(Clone, Debug)]
pub struct CartItem {
    pub id: u32,
    pub name: String,
    pub price: f64,
    pub quantity: u32,
}

#[derive(Clone, Debug)]
pub enum CartAction {
    AddItem(String, f64),
    RemoveItem(u32),
    UpdateQuantity(u32, u32),
    ApplyDiscount(f64),
    SetTaxRate(f64),
    ClearCart,
}

pub fn cart_reducer(state: CartState, action: CartAction) -> CartState {
    let mut new_state = match action {
        CartAction::AddItem(name, price) => {
            let mut items = state.items;
            let next_id = items.iter().map(|i| i.id).max().unwrap_or(0) + 1;
            items.push(CartItem {
                id: next_id,
                name,
                price,
                quantity: 1,
            });
            CartState { items, ..state }
        }
        CartAction::RemoveItem(id) => {
            let items = state
                .items
                .into_iter()
                .filter(|item| item.id != id)
                .collect();
            CartState { items, ..state }
        }
        CartAction::UpdateQuantity(id, quantity) => {
            let items = state
                .items
                .into_iter()
                .map(|mut item| {
                    if item.id == id {
                        item.quantity = quantity;
                    }
                    item
                })
                .collect();
            CartState { items, ..state }
        }
        CartAction::ApplyDiscount(discount) => CartState { discount, ..state },
        CartAction::SetTaxRate(tax_rate) => CartState { tax_rate, ..state },
        CartAction::ClearCart => CartState {
            items: Vec::new(),
            total: 0.0,
            final_total: 0.0,
            ..state
        },
    };

    // Recalculate totals
    new_state.total = new_state
        .items
        .iter()
        .map(|item| item.price * item.quantity as f64)
        .sum();

    let discounted_total = new_state.total - new_state.discount;
    let tax_amount = discounted_total * new_state.tax_rate;
    new_state.final_total = discounted_total + tax_amount;

    new_state
}

pub fn shopping_cart_example() -> impl Into<VNode> {
    let initial_state = CartState {
        items: vec![CartItem {
            id: 1,
            name: "Laptop".to_string(),
            price: 999.99,
            quantity: 1,
        }],
        total: 999.99,
        discount: 0.0,
        tax_rate: 0.08,
        final_total: 1079.99,
    };

    let (state, dispatch) = use_reducer(cart_reducer, initial_state);
    let current_state = state.get();

    // Handle keyboard events
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char('1') => {
                dispatch.call(CartAction::AddItem("Mouse".to_string(), 29.99));
            }
            KeyCode::Char('2') => {
                dispatch.call(CartAction::AddItem("Keyboard".to_string(), 79.99));
            }
            KeyCode::Char('3') => {
                dispatch.call(CartAction::AddItem("Monitor".to_string(), 299.99));
            }
            KeyCode::Char('d') => {
                if let Some(first_item) = current_state.items.first() {
                    dispatch.call(CartAction::RemoveItem(first_item.id));
                }
            }
            KeyCode::Char('+') => {
                if let Some(first_item) = current_state.items.first() {
                    dispatch.call(CartAction::UpdateQuantity(
                        first_item.id,
                        first_item.quantity + 1,
                    ));
                }
            }
            KeyCode::Char('-') => {
                if let Some(first_item) = current_state.items.first() {
                    if first_item.quantity > 1 {
                        dispatch.call(CartAction::UpdateQuantity(
                            first_item.id,
                            first_item.quantity - 1,
                        ));
                    }
                }
            }
            KeyCode::Char('s') => {
                dispatch.call(CartAction::ApplyDiscount(50.0));
            }
            KeyCode::Char('c') => {
                dispatch.call(CartAction::ClearCart);
            }
            _ => {}
        }
    }

    let items_list = if current_state.items.is_empty() {
        "Cart is empty".to_string()
    } else {
        current_state
            .items
            .iter()
            .map(|item| {
                format!(
                    "{} x{} - ${:.2}",
                    item.name,
                    item.quantity,
                    item.price * item.quantity as f64
                )
            })
            .collect::<Vec<_>>()
            .join("\n")
    };

    rsx! {
        <Block
            title="Shopping Cart (use_reducer)"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Yellow)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Left}
            >
                {format!(
                    "Items:\n{}\n\nSubtotal: ${:.2}\nDiscount: ${:.2}\nTax ({:.1}%): ${:.2}\nTotal: ${:.2}\n\nControls:\n1/2/3 : Add items\nd : Remove first\n+/- : Qty first\ns : $50 discount\nc : Clear cart",
                    items_list,
                    current_state.total,
                    current_state.discount,
                    current_state.tax_rate * 100.0,
                    current_state.final_total - current_state.total + current_state.discount,
                    current_state.final_total
                )}
            </Paragraph>
        </Block>
    }
}

/// Main function to run all use_reducer examples
pub fn run_use_reducer_examples() -> Result<(), Box<dyn std::error::Error>> {
    println!("Running use_reducer examples...");
    println!("Press 'q' to quit any example");

    // You can uncomment any of these to run individual examples:
    // render(simple_counter_example)?;
    // render(todo_list_example)?;
    render(shopping_cart_example)?;

    Ok(())
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    run_use_reducer_examples()
}
