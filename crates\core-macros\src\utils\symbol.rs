use std::fmt;

use quote::ToTokens;

/// A wrapper around a static string that provides convenient trait implementations
/// for use in procedural macros.
///
/// This struct is designed to represent identifiers, keywords, or other string literals
/// that need to be converted to tokens in proc-macro contexts.
#[derive(<PERSON><PERSON>, Clone, Debug, PartialEq, Eq, Hash)]
pub struct Symbol(pub &'static str);

impl Symbol {
    /// Creates a new Symbol from a static string.
    pub const fn new(s: &'static str) -> Self {
        Symbol(s)
    }

    /// Returns the inner string slice.
    pub const fn as_str(&self) -> &'static str {
        self.0
    }
}

impl AsRef<str> for Symbol {
    fn as_ref(&self) -> &str {
        self.0
    }
}

impl ToTokens for Symbol {
    fn to_tokens(&self, tokens: &mut proc_macro2::TokenStream) {
        // Check if the string is a valid identifier
        if is_valid_identifier(self.0) {
            // Handle raw identifiers specially
            if let Some(stripped) = self.0.strip_prefix("r#") {
                let ident = proc_macro2::Ident::new_raw(stripped, proc_macro2::Span::call_site());
                ident.to_tokens(tokens);
            } else {
                // Convert the symbol to a regular identifier token
                let ident = proc_macro2::Ident::new(self.0, proc_macro2::Span::call_site());
                ident.to_tokens(tokens);
            }
        } else {
            // If the symbol can't be converted to a valid identifier,
            // emit it as a string literal instead
            let lit = proc_macro2::Literal::string(self.0);
            lit.to_tokens(tokens);
        }
    }
}

/// Check if a string is a valid Rust identifier
fn is_valid_identifier(s: &str) -> bool {
    if s.is_empty() {
        return false;
    }

    // Handle raw identifiers
    if let Some(stripped) = s.strip_prefix("r#") {
        return is_valid_identifier_inner(stripped);
    }

    is_valid_identifier_inner(s)
}

fn is_valid_identifier_inner(s: &str) -> bool {
    let mut chars = s.chars();

    // First character must be a letter or underscore
    match chars.next() {
        Some(c) if c.is_ascii_alphabetic() || c == '_' => {}
        _ => return false,
    }

    // Remaining characters must be letters, digits, or underscores
    chars.all(|c| c.is_ascii_alphanumeric() || c == '_')
}

impl fmt::Display for Symbol {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.0)
    }
}

// Implement common comparison traits for convenience
impl PartialEq<str> for Symbol {
    fn eq(&self, other: &str) -> bool {
        self.0 == other
    }
}

impl PartialEq<&str> for Symbol {
    fn eq(&self, other: &&str) -> bool {
        self.0 == *other
    }
}

impl PartialEq<String> for Symbol {
    fn eq(&self, other: &String) -> bool {
        self.0 == other.as_str()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use quote::quote;

    #[test]
    fn test_symbol_creation() {
        let sym = Symbol("test");
        assert_eq!(sym.0, "test");

        let sym2 = Symbol::new("test2");
        assert_eq!(sym2.0, "test2");
    }

    #[test]
    fn test_symbol_as_ref() {
        let sym = Symbol("hello");
        let s: &str = sym.as_ref();
        assert_eq!(s, "hello");
    }

    #[test]
    fn test_symbol_as_str() {
        let sym = Symbol::new("world");
        assert_eq!(sym.as_str(), "world");
    }

    #[test]
    fn test_symbol_display() {
        let sym = Symbol("display_test");
        assert_eq!(format!("{}", sym), "display_test");
    }

    #[test]
    fn test_symbol_debug() {
        let sym = Symbol("debug_test");
        let debug_str = format!("{:?}", sym);
        assert!(debug_str.contains("debug_test"));
    }

    #[test]
    fn test_symbol_copy_clone() {
        let sym1 = Symbol("copy_test");
        let sym2 = sym1; // Copy
        let sym3 = sym1; // Clone

        assert_eq!(sym1, sym2);
        assert_eq!(sym1, sym3);
        assert_eq!(sym2, sym3);
    }

    #[test]
    fn test_symbol_equality() {
        let sym1 = Symbol("equal");
        let sym2 = Symbol("equal");
        let sym3 = Symbol("different");

        assert_eq!(sym1, sym2);
        assert_ne!(sym1, sym3);
    }

    #[test]
    fn test_symbol_string_comparison() {
        let sym = Symbol("test");

        // Test comparison with &str
        assert_eq!(sym, "test");
        assert_ne!(sym, "other");

        // Test comparison with String
        let string = String::from("test");
        assert_eq!(sym, string);

        let other_string = String::from("other");
        assert_ne!(sym, other_string);
    }

    #[test]
    fn test_symbol_to_tokens() {
        let sym = Symbol("test_identifier");
        let tokens = quote! { #sym };

        // The token stream should contain the identifier
        let token_string = tokens.to_string();
        assert_eq!(token_string.trim(), "test_identifier");
    }

    #[test]
    fn test_symbol_to_tokens_with_raw_identifiers() {
        // Test with raw identifiers (should work as identifiers)
        let sym = Symbol("r#type");
        let tokens = quote! { #sym };

        let token_string = tokens.to_string();
        assert_eq!(token_string.trim(), "r#type");
    }

    #[test]
    fn test_symbol_to_tokens_with_invalid_identifiers() {
        // Test with strings that can't be identifiers (should become string literals)
        let sym = Symbol("123invalid");
        let tokens = quote! { #sym };

        let token_string = tokens.to_string();
        assert_eq!(token_string.trim(), "\"123invalid\"");

        // Test with empty string
        let empty_sym = Symbol("");
        let empty_tokens = quote! { #empty_sym };
        let empty_string = empty_tokens.to_string();
        assert_eq!(empty_string.trim(), "\"\"");

        // Test with special characters
        let special_sym = Symbol("hello-world");
        let special_tokens = quote! { #special_sym };
        let special_string = special_tokens.to_string();
        assert_eq!(special_string.trim(), "\"hello-world\"");
    }

    #[test]
    fn test_is_valid_identifier() {
        // Valid identifiers
        assert!(is_valid_identifier("hello"));
        assert!(is_valid_identifier("_test"));
        assert!(is_valid_identifier("test123"));
        assert!(is_valid_identifier("_"));
        assert!(is_valid_identifier("r#type"));
        assert!(is_valid_identifier("r#match"));

        // Invalid identifiers
        assert!(!is_valid_identifier(""));
        assert!(!is_valid_identifier("123test"));
        assert!(!is_valid_identifier("hello-world"));
        assert!(!is_valid_identifier("hello world"));
        assert!(!is_valid_identifier("hello.world"));
        assert!(!is_valid_identifier("r#"));
        assert!(!is_valid_identifier("r#123"));
    }

    /// Test that demonstrates typical usage in a proc-macro context
    #[test]
    fn test_symbol_proc_macro_usage() {
        let field_name = Symbol("field_name");
        let getter_name = Symbol("get_field_name");

        // Simulate generating getter method tokens
        let tokens = quote! {
            pub fn #getter_name(&self) -> &str {
                &self.#field_name
            }
        };

        let generated = tokens.to_string();
        assert!(generated.contains("get_field_name"));
        assert!(generated.contains("field_name"));
    }
}
