//! Professional use_callback hook implementation
//!
//! This module provides a React-like `use_callback` hook that memoizes callbacks
//! to prevent unnecessary re-renders and optimize performance in TUI applications.
//!
//! The hook integrates with the enhanced Callback API and provides dependency
//! tracking using the same EffectDependencies system as the effect hooks.

use crate::callback::Callback;
use crate::hooks::{effect::EffectDependencies, with_hook_context};

/// A memoized callback that only updates when dependencies change
#[derive(Clone)]
pub struct MemoizedCallback<IN, OUT = ()> {
    callback: Callback<IN, OUT>,
}

impl<IN, OUT> MemoizedCallback<IN, OUT> {
    /// Create a new memoized callback
    fn new(callback: Callback<IN, OUT>) -> Self {
        Self { callback }
    }

    /// Get the underlying callback
    pub fn callback(&self) -> &Callback<IN, OUT> {
        &self.callback
    }

    /// Emit the callback with the given input
    pub fn emit(&self, input: IN) -> OUT {
        self.callback.emit(input)
    }
}

/// Internal state for tracking memoized callbacks
struct CallbackState<IN, OUT> {
    /// Previous dependencies for comparison
    prev_deps: Option<Box<dyn EffectDependencies>>,
    /// The underlying callback
    callback: Option<Callback<IN, OUT>>,
}

impl<IN, OUT> CallbackState<IN, OUT> {
    fn new() -> Self {
        Self {
            prev_deps: None,
            callback: None,
        }
    }
}

/// Trait for types that can be converted into callback factories
///
/// This allows both direct closures and callback factory functions to be used
/// with use_callback, providing a more ergonomic API.
pub trait IntoCallbackFactory<IN, OUT> {
    /// Convert this into a callback factory function
    fn into_factory(self) -> Box<dyn FnOnce() -> Callback<IN, OUT>>;
}

/// Implementation for direct closures - most ergonomic usage
/// This is the primary use case: use_callback(|x| { ... }, deps)
impl<IN, OUT, F> IntoCallbackFactory<IN, OUT> for F
where
    F: Fn(IN) -> OUT + Send + Sync + Clone + 'static,
    IN: 'static,
    OUT: 'static,
{
    fn into_factory(self) -> Box<dyn FnOnce() -> Callback<IN, OUT>> {
        Box::new(move || Callback::from(self))
    }
}

/// Wrapper type for callback factory functions
/// Use this for advanced cases: use_callback(CallbackFactory(|| Callback::from(...)), deps)
pub struct CallbackFactory<F>(pub F);

/// Implementation for callback factory functions - advanced usage
impl<IN, OUT, F> IntoCallbackFactory<IN, OUT> for CallbackFactory<F>
where
    F: FnOnce() -> Callback<IN, OUT> + 'static,
    IN: 'static,
    OUT: 'static,
{
    fn into_factory(self) -> Box<dyn FnOnce() -> Callback<IN, OUT>> {
        Box::new(self.0)
    }
}

/// Professional use_callback hook with dependency tracking
///
/// This hook memoizes a callback function and only recreates it when the dependencies change.
/// This is useful for optimizing performance by preventing unnecessary re-renders of child
/// components that depend on callback props.
///
/// Uses the same dependency system as effect hooks for consistency.
///
/// # Arguments
///
/// * `factory` - A function that creates the callback (can be a closure or Callback factory)
/// * `deps` - Dependencies that the callback depends on (same as useEffect)
///
/// # Examples
///
/// ```rust
/// use rink_core::hooks::callback::use_callback;
/// use rink_core::hooks::state::use_state;
///
/// // Direct closure usage (ergonomic)
/// let (count, set_count) = use_state(0);
/// let count_value = count.get();
/// let increment = use_callback(
///     move |_| set_count.update(|c| c + 1),
///     count_value
/// );
///
/// // Factory function usage (advanced)
/// let submit = use_callback(
///     move || Callback::from(move |_| {
///         println!("Submitting: {} ({})", name_value, age_value);
///     }),
///     (name_value, age_value)
/// );
/// ```
pub fn use_callback<IN, OUT, F, Deps>(
    factory: F,
    deps: impl Into<Option<Deps>>,
) -> MemoizedCallback<IN, OUT>
where
    F: IntoCallbackFactory<IN, OUT> + 'static,
    Deps: EffectDependencies + Clone + PartialEq + 'static,
    IN: 'static,
    OUT: 'static,
{
    let deps = deps.into();
    with_hook_context(|ctx| {
        // Use get_or_init_state like use_state does
        let state_ref = ctx.get_or_init_state(|| CallbackState::<IN, OUT>::new());

        let _should_recreate = {
            let mut state = state_ref.borrow_mut();

            // Determine if callback should be recreated
            let should_recreate = match &deps {
                None => {
                    // No dependencies - only create once (like useCallback with empty deps)
                    state.callback.is_none()
                }
                Some(current_deps) => {
                    // Check if dependencies have changed
                    match &state.prev_deps {
                        None => {
                            // First run - always create
                            true
                        }
                        Some(prev_deps) => {
                            // Compare dependencies using EffectDependencies trait
                            !current_deps.deps_eq(prev_deps.as_ref())
                        }
                    }
                }
            };

            if should_recreate {
                // Create new callback using the factory
                let callback_factory = factory.into_factory();
                let new_callback = callback_factory();
                state.callback = Some(new_callback);

                // Store new dependencies
                if let Some(current_deps) = &deps {
                    state.prev_deps = Some(current_deps.clone_deps());
                } else {
                    state.prev_deps = None;
                }
            }

            should_recreate
        };

        // Get the callback after releasing the mutable borrow and wrap it
        let state = state_ref.borrow();
        let callback = (*state
            .callback
            .as_ref()
            .expect("Callback should be initialized"))
        .clone();

        MemoizedCallback::new(callback)
    })
}

/// Convenience function for use_callback without dependencies
///
/// This creates a callback that never changes, similar to useCallback with an empty
/// dependency array in React.
///
/// # Examples
///
/// ```rust
/// use rink_core::hooks::callback::use_callback_once;
///
/// let log_click = use_callback_once(|msg: String| println!("Clicked: {}", msg));
/// ```
pub fn use_callback_once<IN, OUT, F>(factory: F) -> MemoizedCallback<IN, OUT>
where
    F: IntoCallbackFactory<IN, OUT> + 'static,
    IN: 'static,
    OUT: 'static,
{
    use_callback(factory, ())
}

/// Hook for creating event handlers with automatic memoization
///
/// This is a specialized version of use_callback for event handlers that
/// provides additional type safety and ergonomics.
///
/// # Examples
///
/// ```rust
/// use rink_core::hooks::callback::use_event_handler;
/// use rink_core::hooks::state::use_state;
/// use crossterm::event::KeyCode;
///
/// let (count, set_count) = use_state(0);
/// let count_value = count.get();
/// let on_key_press = use_event_handler(
///     move |key: KeyCode| {
///         match key {
///             KeyCode::Up => set_count.update(|c| c + 1),
///             KeyCode::Down => set_count.update(|c| c - 1),
///             _ => {}
///         }
///     },
///     count_value
/// );
/// ```
pub fn use_event_handler<T, F, Deps>(
    handler: F,
    deps: impl Into<Option<Deps>>,
) -> MemoizedCallback<T>
where
    F: Fn(T) + Clone + Send + Sync + 'static,
    Deps: EffectDependencies + Clone + PartialEq + 'static,
    T: 'static,
{
    use_callback(CallbackFactory(move || Callback::from(handler)), deps)
}

#[cfg(test)]
mod tests;
