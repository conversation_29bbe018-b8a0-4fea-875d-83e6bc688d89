//! Enhanced Callback API Example - Interactive TUI Demo
//!
//! This example demonstrates the comprehensive, standard Rust API for callbacks
//! using the From trait and other ergonomic improvements in a real TUI application.

use crossterm::event::{Event, KeyCode};
use rink::prelude::*;
use rink_core::callback::Callback;

#[derive(Debu<PERSON>, <PERSON><PERSON>, PartialEq)]
enum DemoMode {
    Basic,
    Chained,
    Filtered,
    Constant,
    Mutable,
}

/// Interactive callback demonstration app
fn build_ui() -> impl Into<VNode> {
    let (app, controller) = use_app();

    // Application state
    let (counter, set_counter) = use_state(0i32);
    let (demo_mode, set_demo_mode) = use_state(DemoMode::Basic);
    let (callback_result, set_callback_result) =
        use_state("Press 1-5 to test different callback types".to_string());

    // Create various callback examples using the enhanced API
    let basic_callback: Callback<i32, String> =
        Callback::from(|x| format!("Basic callback result: {}", x));

    let chained_callback: Callback<i32, String> = Callback::from(|x| x + 10)
        .then(|x| x * 2)
        .map(|x| format!("Chained: {} -> +10 -> *2 = {}", x / 2 - 10, x));

    let filtered_callback: Callback<i32, Option<String>> =
        Callback::from(|x| format!("Positive value: {}", x)).filter(|&x| x > 0);

    let constant_callback: Callback<i32, String> =
        Callback::always("Always returns this constant!".to_string());

    // Mutable callback with interior mutability
    let mut call_count = 0;
    let counting_callback: Callback<i32, String> = Callback::from_mut(move |x| {
        call_count += 1;
        format!("Mutable callback - Call #{}: value = {}", call_count, x)
    });

    // Handle keyboard events
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char('q') | KeyCode::Esc => {
                controller.exit(ExitReason::UserRequested);
            }
            KeyCode::Char('1') => {
                set_demo_mode.set(DemoMode::Basic);
                let result = basic_callback.emit(counter.get());
                set_callback_result.set(result);
            }
            KeyCode::Char('2') => {
                set_demo_mode.set(DemoMode::Chained);
                let result = chained_callback.emit(counter.get());
                set_callback_result.set(result);
            }
            KeyCode::Char('3') => {
                set_demo_mode.set(DemoMode::Filtered);
                let result = filtered_callback.emit(counter.get());
                set_callback_result.set(format!("Filtered result: {:?}", result));
            }
            KeyCode::Char('4') => {
                set_demo_mode.set(DemoMode::Constant);
                let result = constant_callback.emit(counter.get());
                set_callback_result.set(result);
            }
            KeyCode::Char('5') => {
                set_demo_mode.set(DemoMode::Mutable);
                let result = counting_callback.emit(counter.get());
                set_callback_result.set(result);
            }
            KeyCode::Up | KeyCode::Char('+') => {
                set_counter.update(|current| current + 1);
            }
            KeyCode::Down | KeyCode::Char('-') => {
                set_counter.update(|current| current - 1);
            }
            KeyCode::Char('r') => {
                set_counter.set(0);
                set_callback_result.set("Counter reset to 0!".to_string());
            }
            _ => {}
        }
    }

    // Handle app exit
    if app.should_exit() {
        return rsx! {
            <Block
                title="Goodbye!"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Red)}
            >
                <Paragraph
                    style={Style::default().fg(Color::White)}
                    alignment={Alignment::Center}
                >
                    {"Thanks for exploring the Enhanced Callback API!\n\n\
                     This demo showcased:\n\
                     • From trait conversions for ergonomic callback creation\n\
                     • Callback chaining with .then() and .map()\n\
                     • Filtering with conditional execution\n\
                     • Constant callbacks that ignore input\n\
                     • Mutable state handling with interior mutability\n\
                     • Standard Rust API patterns and conventions\n\n\
                     The callback system provides a type-safe, ergonomic\n\
                     way to handle events and transformations in TUI apps!"}
                </Paragraph>
            </Block>
        };
    }

    // Get current values
    let counter_value = counter.get();
    let current_mode = demo_mode.get();
    let result_text = callback_result.get();
    let metrics = app.metrics();

    // Choose colors based on demo mode
    let border_color = match current_mode {
        DemoMode::Basic => Color::Cyan,
        DemoMode::Chained => Color::Green,
        DemoMode::Filtered => Color::Yellow,
        DemoMode::Constant => Color::Magenta,
        DemoMode::Mutable => Color::Blue,
    };

    let mode_description = match current_mode {
        DemoMode::Basic => "Basic callback using From trait",
        DemoMode::Chained => "Chained callbacks with .then() and .map()",
        DemoMode::Filtered => "Filtered callback (only positive values)",
        DemoMode::Constant => "Constant callback (ignores input)",
        DemoMode::Mutable => "Mutable callback with interior state",
    };

    rsx! {
        <Block
            title="Enhanced Callback API Demo"
            borders={Borders::ALL}
            border_style={Style::default().fg(border_color)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Left}
            >
                {format!(
                    "🔧 Enhanced Callback API Demonstration 🔧\n\n\
                    Current Counter: {}\n\
                    Active Mode: {:?}\n\
                    Description: {}\n\n\
                    📋 Callback Result:\n\
                    {}\n\n\
                    🎮 Controls:\n\
                    1-5 : Test different callback types\n\
                    ↑/+ : Increment counter\n\
                    ↓/- : Decrement counter\n\
                    r   : Reset counter\n\
                    q   : Quit\n\n\
                    📊 Features Demonstrated:\n\
                    • From<F> trait for ergonomic callback creation\n\
                    • Callback chaining and transformation\n\
                    • Filtering and conditional execution\n\
                    • Constant callbacks and mutable state\n\
                    • Type-safe callback composition\n\n\
                    Renders: {} | Events: {}",
                    counter_value,
                    current_mode,
                    mode_description,
                    result_text,
                    metrics.render_count,
                    metrics.event_count
                )}
            </Paragraph>
        </Block>
    }
}

/// Entry point of the application
fn main() -> Result<(), Box<dyn std::error::Error>> {
    render(build_ui)
}

/// Example of using callbacks in a component-like structure
#[allow(dead_code)]
struct Button {
    label: String,
    on_click: Option<Callback<String>>,
    on_hover: Option<Callback<String>>,
}

#[allow(dead_code)]
impl Button {
    fn new(label: impl Into<String>) -> Self {
        Self {
            label: label.into(),
            on_click: None,
            on_hover: None,
        }
    }

    /// Ergonomic builder method using From trait
    fn on_click<F>(mut self, callback: F) -> Self
    where
        F: Into<Callback<String>>,
    {
        self.on_click = Some(callback.into());
        self
    }

    /// Another ergonomic builder method
    fn on_hover<F>(mut self, callback: F) -> Self
    where
        F: Into<Callback<String>>,
    {
        self.on_hover = Some(callback.into());
        self
    }

    fn click(&self) {
        if let Some(ref callback) = self.on_click {
            callback.emit(self.label.clone());
        }
    }

    fn hover(&self) {
        if let Some(ref callback) = self.on_hover {
            callback.emit(self.label.clone());
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::{Arc, Mutex};

    #[test]
    fn test_button_with_callbacks() {
        let clicked = Arc::new(Mutex::new(false));
        let clicked_clone = clicked.clone();

        let button = Button::new("Test Button")
            .on_click(move |label| {
                println!("Button '{}' clicked!", label);
                *clicked_clone.lock().unwrap() = true;
            })
            .on_hover(|label| println!("Hovering over '{}'", label));

        button.click();
        button.hover();

        assert!(*clicked.lock().unwrap());
    }

    #[test]
    fn test_callback_composition() {
        let result = Callback::from(|x: i32| x + 1)
            .then(|x| x * 2)
            .map(|x| format!("Final: {}", x))
            .emit(5);

        assert_eq!(result, "Final: 12");
    }
}
