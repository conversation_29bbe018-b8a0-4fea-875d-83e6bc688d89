//! Rich Text Showcase - Advanced RSX Text Formatting Demo
//!
//! This example demonstrates the full power of text composition in the TUI framework,
//! showcasing creative uses of Paragraph, Line, and Span components with RSX syntax.
//!
//! Features demonstrated:
//! - Syntax highlighting simulation
//! - Multi-colored status displays  
//! - Interactive text with visual states
//! - Text-based progress indicators
//! - Formatted logs and console output
//! - Rich documentation layouts
//! - Creative text art and styling
//!
//! Controls:
//! - 1-7: Switch between different text formatting examples
//! - Q: Quit the application
//! - Space: Toggle interactive states where applicable

use crossterm::event::{Event, KeyCode, KeyEventKind};
use ratatui::{
    layout::{Alignment, Constraint, Direction},
    style::{Color, Modifier, Style},
    text::{Line, Span},
    widgets::{Block, Borders, Paragraph},
};
use rink::{VNode, render_async, rsx, use_app, use_event, use_state};
use std::time::Instant;

/// Available text formatting examples
#[derive(Debug, <PERSON>lone, Copy, PartialEq)]
enum TextExample {
    SyntaxHighlighting,
    StatusDisplay,
    InteractiveText,
    ProgressIndicators,
    FormattedLogs,
    Documentation,
    TextArt,
}

impl TextExample {
    fn all() -> &'static [TextExample] {
        &[
            TextExample::SyntaxHighlighting,
            TextExample::StatusDisplay,
            TextExample::InteractiveText,
            TextExample::ProgressIndicators,
            TextExample::FormattedLogs,
            TextExample::Documentation,
            TextExample::TextArt,
        ]
    }

    fn title(&self) -> &'static str {
        match self {
            TextExample::SyntaxHighlighting => "1. Syntax Highlighting",
            TextExample::StatusDisplay => "2. Status Display",
            TextExample::InteractiveText => "3. Interactive Text",
            TextExample::ProgressIndicators => "4. Progress Indicators",
            TextExample::FormattedLogs => "5. Formatted Logs",
            TextExample::Documentation => "6. Documentation",
            TextExample::TextArt => "7. Text Art",
        }
    }

    fn description(&self) -> &'static str {
        match self {
            TextExample::SyntaxHighlighting => {
                "Code syntax highlighting with multiple colors and styles"
            }
            TextExample::StatusDisplay => {
                "System status with colored indicators and real-time updates"
            }
            TextExample::InteractiveText => {
                "Text that changes appearance based on user interaction"
            }
            TextExample::ProgressIndicators => {
                "Visual progress bars and charts using text characters"
            }
            TextExample::FormattedLogs => {
                "Structured log output with timestamps and severity levels"
            }
            TextExample::Documentation => "Rich documentation with headers, lists, and emphasis",
            TextExample::TextArt => "Creative text art and decorative elements",
        }
    }
}

/// Application state
#[derive(Debug, Clone)]
struct AppState {
    current_example: TextExample,
    interactive_state: bool,
    start_time: Instant,
    progress: f32,
}

impl PartialEq for AppState {
    fn eq(&self, other: &Self) -> bool {
        self.current_example == other.current_example
            && self.interactive_state == other.interactive_state
            && self.progress == other.progress
        // Skip start_time comparison as Instant doesn't implement PartialEq
    }
}

impl Default for AppState {
    fn default() -> Self {
        Self {
            current_example: TextExample::SyntaxHighlighting,
            interactive_state: false,
            start_time: Instant::now(),
            progress: 0.0,
        }
    }
}

/// Main application component
fn rich_text_showcase() -> VNode {
    let (_app, controller) = use_app();
    let (state, set_state) = use_state(AppState::default());

    // Update progress for animated examples
    let current_state = state.get();
    let elapsed = current_state.start_time.elapsed().as_secs_f32();
    let new_progress = (elapsed * 0.2).sin().abs(); // Smooth oscillation
    if (new_progress - current_state.progress).abs() > 0.01 {
        let mut new_state = current_state.clone();
        new_state.progress = new_progress;
        set_state.call(new_state);
    }

    // Handle keyboard input
    if let Some(Event::Key(key)) = use_event() {
        if key.kind == KeyEventKind::Press {
            match key.code {
                KeyCode::Char('q') => controller.shutdown(),
                KeyCode::Char(' ') => {
                    let mut new_state = state.get().clone();
                    new_state.interactive_state = !new_state.interactive_state;
                    set_state.call(new_state);
                }
                KeyCode::Char('1') => {
                    let mut new_state = state.get().clone();
                    new_state.current_example = TextExample::SyntaxHighlighting;
                    set_state.call(new_state);
                }
                KeyCode::Char('2') => {
                    let mut new_state = state.get().clone();
                    new_state.current_example = TextExample::StatusDisplay;
                    set_state.call(new_state);
                }
                KeyCode::Char('3') => {
                    let mut new_state = state.get().clone();
                    new_state.current_example = TextExample::InteractiveText;
                    set_state.call(new_state);
                }
                KeyCode::Char('4') => {
                    let mut new_state = state.get().clone();
                    new_state.current_example = TextExample::ProgressIndicators;
                    set_state.call(new_state);
                }
                KeyCode::Char('5') => {
                    let mut new_state = state.get().clone();
                    new_state.current_example = TextExample::FormattedLogs;
                    set_state.call(new_state);
                }
                KeyCode::Char('6') => {
                    let mut new_state = state.get().clone();
                    new_state.current_example = TextExample::Documentation;
                    set_state.call(new_state);
                }
                KeyCode::Char('7') => {
                    let mut new_state = state.get().clone();
                    new_state.current_example = TextExample::TextArt;
                    set_state.call(new_state);
                }
                _ => {}
            }
        }
    }

    let current_state = state.get();

    rsx! {
        <Layout
            direction={Direction::Vertical}
            constraints={vec![
                Constraint::Length(3),    // Header
                Constraint::Length(5),    // Example selector
                Constraint::Min(10),      // Main content
                Constraint::Length(3),    // Footer
            ]}
        >
            {render_header()}
            {render_example_selector(&current_state)}
            {render_main_content(&current_state)}
            {render_footer(&current_state)}
        </Layout>
    }
}

/// Render the application header
fn render_header() -> VNode {
    rsx! {
        <Block
            title="🎨 Rich Text Showcase - Advanced RSX Formatting Demo"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Cyan)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Center}
            >
                {"Demonstrating the full power of Paragraph, Line, and Span composition with RSX"}
            </Paragraph>
        </Block>
    }
}

/// Render the example selector
fn render_example_selector(state: &AppState) -> VNode {
    // Create a line with all example options, highlighting the current one
    let example_spans: Vec<Span> = TextExample::all()
        .iter()
        .enumerate()
        .flat_map(|(i, example)| {
            let is_current = *example == state.current_example;
            let style = if is_current {
                Style::default()
                    .fg(Color::Yellow)
                    .add_modifier(Modifier::BOLD | Modifier::UNDERLINED)
            } else {
                Style::default().fg(Color::Gray)
            };

            let mut spans = vec![Span::styled(example.title(), style)];
            if i < TextExample::all().len() - 1 {
                spans.push(Span::raw(" | "));
            }
            spans
        })
        .collect();

    let description_line = Line::from(vec![
        Span::styled(
            "Description: ",
            Style::default()
                .fg(Color::Cyan)
                .add_modifier(Modifier::BOLD),
        ),
        Span::styled(
            state.current_example.description(),
            Style::default().fg(Color::White),
        ),
    ]);

    rsx! {
        <Block
            title="📋 Examples"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Blue)}
        >
            <Paragraph>
                {vec![
                    Line::from(example_spans),
                    Line::raw(""),
                    description_line,
                ]}
            </Paragraph>
        </Block>
    }
}

/// Render the main content area based on selected example
fn render_main_content(state: &AppState) -> VNode {
    match state.current_example {
        TextExample::SyntaxHighlighting => render_syntax_highlighting(),
        TextExample::StatusDisplay => render_status_display(state),
        TextExample::InteractiveText => render_interactive_text(state),
        TextExample::ProgressIndicators => render_progress_indicators(state),
        TextExample::FormattedLogs => render_formatted_logs(state),
        TextExample::Documentation => render_documentation(),
        TextExample::TextArt => render_text_art(state),
    }
}

/// Render the footer with controls
fn render_footer(state: &AppState) -> VNode {
    let controls = vec![
        Span::styled(
            "Controls: ",
            Style::default()
                .fg(Color::Cyan)
                .add_modifier(Modifier::BOLD),
        ),
        Span::styled(
            "1-7",
            Style::default()
                .fg(Color::Yellow)
                .add_modifier(Modifier::BOLD),
        ),
        Span::raw(": Switch examples | "),
        Span::styled(
            "Space",
            Style::default()
                .fg(Color::Yellow)
                .add_modifier(Modifier::BOLD),
        ),
        Span::raw(": Toggle interactive | "),
        Span::styled(
            "Q",
            Style::default()
                .fg(Color::Yellow)
                .add_modifier(Modifier::BOLD),
        ),
        Span::raw(": Quit"),
    ];

    let status = if state.interactive_state {
        Span::styled(
            " [INTERACTIVE ON]",
            Style::default()
                .fg(Color::Green)
                .add_modifier(Modifier::BOLD),
        )
    } else {
        Span::styled(" [INTERACTIVE OFF]", Style::default().fg(Color::Red))
    };

    rsx! {
        <Block
            title="🎮 Controls"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Gray)}
        >
            <Paragraph>
                {vec![Line::from([controls, vec![status]].concat())]}
            </Paragraph>
        </Block>
    }
}

/// Render syntax highlighting example
fn render_syntax_highlighting() -> VNode {
    // Simulate Rust code syntax highlighting
    let code_lines = vec![
        Line::from(vec![
            Span::styled(
                "use ",
                Style::default()
                    .fg(Color::Magenta)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::styled("std", Style::default().fg(Color::Blue)),
            Span::raw("::"),
            Span::styled("collections", Style::default().fg(Color::Blue)),
            Span::raw("::"),
            Span::styled("HashMap", Style::default().fg(Color::Cyan)),
            Span::raw(";"),
        ]),
        Line::raw(""),
        Line::from(vec![
            Span::styled(
                "fn ",
                Style::default()
                    .fg(Color::Magenta)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::styled(
                "main",
                Style::default()
                    .fg(Color::Yellow)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::raw("() {"),
        ]),
        Line::from(vec![
            Span::raw("    "),
            Span::styled(
                "let ",
                Style::default()
                    .fg(Color::Magenta)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::styled(
                "mut ",
                Style::default()
                    .fg(Color::Magenta)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::styled("map", Style::default().fg(Color::White)),
            Span::raw(" = "),
            Span::styled("HashMap", Style::default().fg(Color::Cyan)),
            Span::raw("::"),
            Span::styled("new", Style::default().fg(Color::Yellow)),
            Span::raw("();"),
        ]),
        Line::from(vec![
            Span::raw("    "),
            Span::styled("map", Style::default().fg(Color::White)),
            Span::raw("."),
            Span::styled("insert", Style::default().fg(Color::Yellow)),
            Span::raw("("),
            Span::styled("\"key\"", Style::default().fg(Color::Green)),
            Span::raw(", "),
            Span::styled("42", Style::default().fg(Color::Red)),
            Span::raw(");"),
        ]),
        Line::from(vec![
            Span::raw("    "),
            Span::styled(
                "println!",
                Style::default()
                    .fg(Color::Magenta)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::raw("("),
            Span::styled("\"Value: {}\"", Style::default().fg(Color::Green)),
            Span::raw(", "),
            Span::styled("map", Style::default().fg(Color::White)),
            Span::raw("["),
            Span::styled("\"key\"", Style::default().fg(Color::Green)),
            Span::raw("]);"),
        ]),
        Line::raw("}"),
        Line::raw(""),
        Line::from(vec![
            Span::styled(
                "// ",
                Style::default()
                    .fg(Color::Gray)
                    .add_modifier(Modifier::ITALIC),
            ),
            Span::styled(
                "This demonstrates syntax highlighting with:",
                Style::default()
                    .fg(Color::Gray)
                    .add_modifier(Modifier::ITALIC),
            ),
        ]),
        Line::from(vec![
            Span::styled(
                "// ",
                Style::default()
                    .fg(Color::Gray)
                    .add_modifier(Modifier::ITALIC),
            ),
            Span::styled(
                "- Keywords in magenta/bold",
                Style::default()
                    .fg(Color::Gray)
                    .add_modifier(Modifier::ITALIC),
            ),
        ]),
        Line::from(vec![
            Span::styled(
                "// ",
                Style::default()
                    .fg(Color::Gray)
                    .add_modifier(Modifier::ITALIC),
            ),
            Span::styled(
                "- Types in cyan",
                Style::default()
                    .fg(Color::Gray)
                    .add_modifier(Modifier::ITALIC),
            ),
        ]),
        Line::from(vec![
            Span::styled(
                "// ",
                Style::default()
                    .fg(Color::Gray)
                    .add_modifier(Modifier::ITALIC),
            ),
            Span::styled(
                "- Functions in yellow",
                Style::default()
                    .fg(Color::Gray)
                    .add_modifier(Modifier::ITALIC),
            ),
        ]),
        Line::from(vec![
            Span::styled(
                "// ",
                Style::default()
                    .fg(Color::Gray)
                    .add_modifier(Modifier::ITALIC),
            ),
            Span::styled(
                "- Strings in green",
                Style::default()
                    .fg(Color::Gray)
                    .add_modifier(Modifier::ITALIC),
            ),
        ]),
        Line::from(vec![
            Span::styled(
                "// ",
                Style::default()
                    .fg(Color::Gray)
                    .add_modifier(Modifier::ITALIC),
            ),
            Span::styled(
                "- Numbers in red",
                Style::default()
                    .fg(Color::Gray)
                    .add_modifier(Modifier::ITALIC),
            ),
        ]),
    ];

    rsx! {
        <Block
            title="💻 Syntax Highlighting Example"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Green)}
        >
            <Paragraph>
                {code_lines}
            </Paragraph>
        </Block>
    }
}

/// Render status display example
fn render_status_display(state: &AppState) -> VNode {
    // Simulate system status with real-time updates
    let uptime_seconds = state.start_time.elapsed().as_secs();
    let cpu_usage = (state.progress * 100.0) as u8;
    let memory_usage = ((state.progress * 0.7 + 0.3) * 100.0) as u8;

    let status_lines = vec![
        Line::from(vec![Span::styled(
            "🖥️  SYSTEM STATUS",
            Style::default()
                .fg(Color::Cyan)
                .add_modifier(Modifier::BOLD | Modifier::UNDERLINED),
        )]),
        Line::raw(""),
        Line::from(vec![
            Span::styled(
                "Uptime: ",
                Style::default()
                    .fg(Color::White)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::styled(
                format!("{}s", uptime_seconds),
                Style::default().fg(Color::Green),
            ),
            Span::raw("  |  "),
            Span::styled(
                "Status: ",
                Style::default()
                    .fg(Color::White)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::styled(
                "🟢 ONLINE",
                Style::default()
                    .fg(Color::Green)
                    .add_modifier(Modifier::BOLD),
            ),
        ]),
        Line::raw(""),
        Line::from(vec![
            Span::styled(
                "CPU Usage: ",
                Style::default()
                    .fg(Color::White)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::styled(
                format!("{}%", cpu_usage),
                if cpu_usage > 80 {
                    Style::default().fg(Color::Red).add_modifier(Modifier::BOLD)
                } else if cpu_usage > 60 {
                    Style::default()
                        .fg(Color::Yellow)
                        .add_modifier(Modifier::BOLD)
                } else {
                    Style::default()
                        .fg(Color::Green)
                        .add_modifier(Modifier::BOLD)
                },
            ),
            Span::raw("  "),
            Span::raw("█".repeat((cpu_usage / 5) as usize)),
            Span::styled(
                "░".repeat(20 - (cpu_usage / 5) as usize),
                Style::default().fg(Color::DarkGray),
            ),
        ]),
        Line::from(vec![
            Span::styled(
                "Memory:    ",
                Style::default()
                    .fg(Color::White)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::styled(
                format!("{}%", memory_usage),
                if memory_usage > 80 {
                    Style::default().fg(Color::Red).add_modifier(Modifier::BOLD)
                } else if memory_usage > 60 {
                    Style::default()
                        .fg(Color::Yellow)
                        .add_modifier(Modifier::BOLD)
                } else {
                    Style::default()
                        .fg(Color::Green)
                        .add_modifier(Modifier::BOLD)
                },
            ),
            Span::raw("  "),
            Span::styled(
                "█".repeat((memory_usage / 5) as usize),
                Style::default().fg(Color::Blue),
            ),
            Span::styled(
                "░".repeat(20 - (memory_usage / 5) as usize),
                Style::default().fg(Color::DarkGray),
            ),
        ]),
        Line::raw(""),
        Line::from(vec![Span::styled(
            "🌐 SERVICES",
            Style::default()
                .fg(Color::Cyan)
                .add_modifier(Modifier::BOLD | Modifier::UNDERLINED),
        )]),
        Line::raw(""),
        Line::from(vec![
            Span::styled("Web Server:    ", Style::default().fg(Color::White)),
            Span::styled(
                "🟢 Running",
                Style::default()
                    .fg(Color::Green)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::raw("    Port: "),
            Span::styled("8080", Style::default().fg(Color::Cyan)),
        ]),
        Line::from(vec![
            Span::styled("Database:      ", Style::default().fg(Color::White)),
            Span::styled(
                "🟢 Connected",
                Style::default()
                    .fg(Color::Green)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::raw("  Pool: "),
            Span::styled("15/20", Style::default().fg(Color::Yellow)),
        ]),
        Line::from(vec![
            Span::styled("Cache:         ", Style::default().fg(Color::White)),
            Span::styled(
                "🟡 Warning",
                Style::default()
                    .fg(Color::Yellow)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::raw("    Hit Rate: "),
            Span::styled("87%", Style::default().fg(Color::Yellow)),
        ]),
        Line::from(vec![
            Span::styled("Load Balancer: ", Style::default().fg(Color::White)),
            if state.interactive_state {
                Span::styled(
                    "🔴 Maintenance",
                    Style::default().fg(Color::Red).add_modifier(Modifier::BOLD),
                )
            } else {
                Span::styled(
                    "🟢 Active",
                    Style::default()
                        .fg(Color::Green)
                        .add_modifier(Modifier::BOLD),
                )
            },
            Span::raw("  Nodes: "),
            Span::styled("3/4", Style::default().fg(Color::Green)),
        ]),
    ];

    rsx! {
        <Block
            title="📊 System Status Dashboard"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Blue)}
        >
            <Paragraph>
                {status_lines}
            </Paragraph>
        </Block>
    }
}

/// Render interactive text example
fn render_interactive_text(state: &AppState) -> VNode {
    let interactive_lines = vec![
        Line::from(vec![Span::styled(
            "🎯 INTERACTIVE TEXT DEMO",
            Style::default()
                .fg(Color::Cyan)
                .add_modifier(Modifier::BOLD | Modifier::UNDERLINED),
        )]),
        Line::raw(""),
        Line::from(vec![
            Span::styled(
                "Press SPACE to toggle interactive mode: ",
                Style::default().fg(Color::White),
            ),
            if state.interactive_state {
                Span::styled(
                    "ACTIVE",
                    Style::default()
                        .fg(Color::Green)
                        .add_modifier(Modifier::BOLD | Modifier::RAPID_BLINK),
                )
            } else {
                Span::styled("INACTIVE", Style::default().fg(Color::Red))
            },
        ]),
        Line::raw(""),
        Line::from(vec![
            Span::styled("Button State: ", Style::default().fg(Color::White)),
            if state.interactive_state {
                Span::styled(
                    "🟢 PRESSED",
                    Style::default()
                        .bg(Color::Green)
                        .fg(Color::Black)
                        .add_modifier(Modifier::BOLD),
                )
            } else {
                Span::styled(
                    "⚪ RELEASED",
                    Style::default().bg(Color::Gray).fg(Color::White),
                )
            },
        ]),
        Line::raw(""),
        Line::from(vec![
            Span::styled("Hover Effect: ", Style::default().fg(Color::White)),
            if state.interactive_state {
                Span::styled(
                    "✨ HIGHLIGHTED ✨",
                    Style::default()
                        .fg(Color::Yellow)
                        .add_modifier(Modifier::BOLD | Modifier::UNDERLINED),
                )
            } else {
                Span::styled("Normal text", Style::default().fg(Color::Gray))
            },
        ]),
        Line::raw(""),
        Line::from(vec![
            Span::styled("Status Indicator: ", Style::default().fg(Color::White)),
            if state.interactive_state {
                Span::styled(
                    "🔥 HOT",
                    Style::default().fg(Color::Red).add_modifier(Modifier::BOLD),
                )
            } else {
                Span::styled("❄️ COOL", Style::default().fg(Color::Blue))
            },
        ]),
        Line::raw(""),
        Line::from(vec![
            Span::styled("Dynamic Content: ", Style::default().fg(Color::White)),
            if state.interactive_state {
                Span::styled("Loading", Style::default().fg(Color::Yellow))
            } else {
                Span::styled("Ready", Style::default().fg(Color::Green))
            },
            if state.interactive_state {
                Span::styled(
                    " ⣾⣽⣻⢿⡿⣟⣯⣷",
                    Style::default()
                        .fg(Color::Cyan)
                        .add_modifier(Modifier::RAPID_BLINK),
                )
            } else {
                Span::raw("")
            },
        ]),
    ];

    rsx! {
        <Block
            title="🎯 Interactive Text States"
            borders={Borders::ALL}
            border_style={if state.interactive_state {
                Style::default().fg(Color::Green)
            } else {
                Style::default().fg(Color::Gray)
            }}
        >
            <Paragraph>
                {interactive_lines}
            </Paragraph>
        </Block>
    }
}

/// Render progress indicators example
fn render_progress_indicators(state: &AppState) -> VNode {
    let progress_percent = (state.progress * 100.0) as u8;
    let bar_width = 30;
    let filled_width = ((state.progress * bar_width as f32) as usize).min(bar_width);
    let empty_width = bar_width - filled_width;

    let progress_lines = vec![
        Line::from(vec![Span::styled(
            "📊 PROGRESS INDICATORS",
            Style::default()
                .fg(Color::Cyan)
                .add_modifier(Modifier::BOLD | Modifier::UNDERLINED),
        )]),
        Line::raw(""),
        Line::from(vec![
            Span::styled(
                "Classic Progress Bar: ",
                Style::default()
                    .fg(Color::White)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::styled(
                format!("{}%", progress_percent),
                Style::default().fg(Color::Yellow),
            ),
        ]),
        Line::from(vec![
            Span::raw("["),
            Span::styled("█".repeat(filled_width), Style::default().fg(Color::Green)),
            Span::styled(
                "░".repeat(empty_width),
                Style::default().fg(Color::DarkGray),
            ),
            Span::raw("]"),
        ]),
        Line::raw(""),
        Line::from(vec![Span::styled(
            "Fancy Progress Bar: ",
            Style::default()
                .fg(Color::White)
                .add_modifier(Modifier::BOLD),
        )]),
        Line::from(vec![
            Span::raw("▐"),
            Span::styled("▓".repeat(filled_width), Style::default().fg(Color::Blue)),
            Span::styled("▒".repeat(empty_width), Style::default().fg(Color::Gray)),
            Span::raw("▌"),
            Span::raw("  "),
            Span::styled(
                format!("{:.1}%", state.progress * 100.0),
                Style::default()
                    .fg(Color::Cyan)
                    .add_modifier(Modifier::BOLD),
            ),
        ]),
        Line::raw(""),
        Line::from(vec![
            Span::styled(
                "Circular Progress: ",
                Style::default()
                    .fg(Color::White)
                    .add_modifier(Modifier::BOLD),
            ),
            if progress_percent < 25 {
                Span::styled("◐", Style::default().fg(Color::Red))
            } else if progress_percent < 50 {
                Span::styled("◓", Style::default().fg(Color::Yellow))
            } else if progress_percent < 75 {
                Span::styled("◑", Style::default().fg(Color::Blue))
            } else {
                Span::styled("●", Style::default().fg(Color::Green))
            },
            Span::raw("  "),
            Span::styled(
                format!("{}%", progress_percent),
                Style::default().fg(Color::White),
            ),
        ]),
        Line::raw(""),
        Line::from(vec![
            Span::styled(
                "Animated Spinner: ",
                Style::default()
                    .fg(Color::White)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::styled(
                match (state.start_time.elapsed().as_millis() / 200) % 4 {
                    0 => "⠋",
                    1 => "⠙",
                    2 => "⠹",
                    _ => "⠸",
                },
                Style::default()
                    .fg(Color::Cyan)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::raw("  Processing..."),
        ]),
        Line::raw(""),
        Line::from(vec![Span::styled(
            "Multi-level Progress:",
            Style::default()
                .fg(Color::White)
                .add_modifier(Modifier::BOLD),
        )]),
        Line::from(vec![
            Span::styled("  Task 1: ", Style::default().fg(Color::Gray)),
            Span::styled(
                "✓ Complete",
                Style::default()
                    .fg(Color::Green)
                    .add_modifier(Modifier::BOLD),
            ),
        ]),
        Line::from(vec![
            Span::styled("  Task 2: ", Style::default().fg(Color::Gray)),
            if progress_percent > 30 {
                Span::styled(
                    "✓ Complete",
                    Style::default()
                        .fg(Color::Green)
                        .add_modifier(Modifier::BOLD),
                )
            } else {
                Span::styled("⏳ In Progress", Style::default().fg(Color::Yellow))
            },
        ]),
        Line::from(vec![
            Span::styled("  Task 3: ", Style::default().fg(Color::Gray)),
            if progress_percent > 70 {
                Span::styled("⏳ In Progress", Style::default().fg(Color::Yellow))
            } else {
                Span::styled("⏸ Pending", Style::default().fg(Color::Gray))
            },
        ]),
    ];

    rsx! {
        <Block
            title="📊 Progress Indicators & Charts"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Magenta)}
        >
            <Paragraph>
                {progress_lines}
            </Paragraph>
        </Block>
    }
}

/// Render formatted logs example
fn render_formatted_logs(state: &AppState) -> VNode {
    let log_lines = vec![
        Line::from(vec![Span::styled(
            "📝 FORMATTED LOGS",
            Style::default()
                .fg(Color::Cyan)
                .add_modifier(Modifier::BOLD | Modifier::UNDERLINED),
        )]),
        Line::raw(""),
        Line::from(vec![
            Span::styled("[2024-01-15 10:30:15] ", Style::default().fg(Color::Gray)),
            Span::styled(
                "INFO ",
                Style::default()
                    .fg(Color::Green)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::styled(
                "Application started successfully",
                Style::default().fg(Color::White),
            ),
        ]),
        Line::from(vec![
            Span::styled("[2024-01-15 10:30:16] ", Style::default().fg(Color::Gray)),
            Span::styled(
                "DEBUG",
                Style::default()
                    .fg(Color::Blue)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::styled(
                " Loading configuration from ",
                Style::default().fg(Color::White),
            ),
            Span::styled(
                "config.toml",
                Style::default()
                    .fg(Color::Cyan)
                    .add_modifier(Modifier::ITALIC),
            ),
        ]),
        Line::from(vec![
            Span::styled("[2024-01-15 10:30:17] ", Style::default().fg(Color::Gray)),
            Span::styled(
                "WARN ",
                Style::default()
                    .fg(Color::Yellow)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::styled(
                "High memory usage detected: ",
                Style::default().fg(Color::White),
            ),
            Span::styled(
                "85%",
                Style::default()
                    .fg(Color::Yellow)
                    .add_modifier(Modifier::BOLD),
            ),
        ]),
        Line::from(vec![
            Span::styled("[2024-01-15 10:30:18] ", Style::default().fg(Color::Gray)),
            Span::styled(
                "ERROR",
                Style::default().fg(Color::Red).add_modifier(Modifier::BOLD),
            ),
            Span::styled(
                " Failed to connect to database: ",
                Style::default().fg(Color::White),
            ),
            Span::styled(
                "Connection timeout",
                Style::default()
                    .fg(Color::Red)
                    .add_modifier(Modifier::ITALIC),
            ),
        ]),
        Line::from(vec![
            Span::styled("[2024-01-15 10:30:19] ", Style::default().fg(Color::Gray)),
            Span::styled(
                "INFO ",
                Style::default()
                    .fg(Color::Green)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::styled(
                "Retrying connection... (",
                Style::default().fg(Color::White),
            ),
            Span::styled("attempt 2/3", Style::default().fg(Color::Cyan)),
            Span::styled(")", Style::default().fg(Color::White)),
        ]),
        Line::from(vec![
            Span::styled("[2024-01-15 10:30:20] ", Style::default().fg(Color::Gray)),
            Span::styled(
                "INFO ",
                Style::default()
                    .fg(Color::Green)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::styled(
                "✓ Database connection established",
                Style::default().fg(Color::Green),
            ),
        ]),
        Line::raw(""),
        Line::from(vec![
            Span::styled(
                "Real-time Log: ",
                Style::default()
                    .fg(Color::White)
                    .add_modifier(Modifier::BOLD),
            ),
            if state.interactive_state {
                Span::styled(
                    "🔴 LIVE",
                    Style::default()
                        .fg(Color::Red)
                        .add_modifier(Modifier::RAPID_BLINK),
                )
            } else {
                Span::styled("⏸ PAUSED", Style::default().fg(Color::Gray))
            },
        ]),
        if state.interactive_state {
            Line::from(vec![
                Span::styled(
                    format!(
                        "[{}] ",
                        std::time::SystemTime::now()
                            .duration_since(std::time::UNIX_EPOCH)
                            .unwrap()
                            .as_secs()
                            % 86400
                            / 3600
                    ),
                    Style::default().fg(Color::Gray),
                ),
                Span::styled(
                    "TRACE",
                    Style::default()
                        .fg(Color::Magenta)
                        .add_modifier(Modifier::BOLD),
                ),
                Span::styled(
                    " User interaction detected",
                    Style::default().fg(Color::White),
                ),
            ])
        } else {
            Line::raw("")
        },
    ];

    rsx! {
        <Block
            title="📝 Structured Log Output"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Yellow)}
        >
            <Paragraph>
                {log_lines}
            </Paragraph>
        </Block>
    }
}

/// Render documentation example
fn render_documentation() -> VNode {
    let doc_lines = vec![
        Line::from(vec![Span::styled(
            "📚 RICH DOCUMENTATION",
            Style::default()
                .fg(Color::Cyan)
                .add_modifier(Modifier::BOLD | Modifier::UNDERLINED),
        )]),
        Line::raw(""),
        Line::from(vec![
            Span::styled(
                "# ",
                Style::default()
                    .fg(Color::Blue)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::styled(
                "Getting Started",
                Style::default()
                    .fg(Color::White)
                    .add_modifier(Modifier::BOLD),
            ),
        ]),
        Line::raw(""),
        Line::from(vec![
            Span::raw("Welcome to the "),
            Span::styled(
                "TUI Framework",
                Style::default()
                    .fg(Color::Cyan)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::raw(" documentation. This framework provides:"),
        ]),
        Line::raw(""),
        Line::from(vec![
            Span::styled(
                "• ",
                Style::default()
                    .fg(Color::Green)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::styled(
                "RSX syntax",
                Style::default()
                    .fg(Color::Yellow)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::raw(" for declarative UI"),
        ]),
        Line::from(vec![
            Span::styled(
                "• ",
                Style::default()
                    .fg(Color::Green)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::styled(
                "Component system",
                Style::default()
                    .fg(Color::Yellow)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::raw(" with hooks"),
        ]),
        Line::from(vec![
            Span::styled(
                "• ",
                Style::default()
                    .fg(Color::Green)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::styled(
                "Rich text formatting",
                Style::default()
                    .fg(Color::Yellow)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::raw(" capabilities"),
        ]),
        Line::raw(""),
        Line::from(vec![
            Span::styled(
                "## ",
                Style::default()
                    .fg(Color::Blue)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::styled(
                "Code Example",
                Style::default()
                    .fg(Color::White)
                    .add_modifier(Modifier::BOLD),
            ),
        ]),
        Line::raw(""),
        Line::from(vec![Span::styled(
            "```rust",
            Style::default()
                .fg(Color::Gray)
                .add_modifier(Modifier::ITALIC),
        )]),
        Line::from(vec![Span::styled(
            "rsx! {",
            Style::default()
                .fg(Color::Magenta)
                .add_modifier(Modifier::BOLD),
        )]),
        Line::from(vec![
            Span::raw("    "),
            Span::styled("<Paragraph>", Style::default().fg(Color::Blue)),
        ]),
        Line::from(vec![
            Span::raw("        "),
            Span::styled("{vec![", Style::default().fg(Color::Cyan)),
        ]),
        Line::from(vec![
            Span::raw("            "),
            Span::styled("Line::from", Style::default().fg(Color::Yellow)),
            Span::raw("("),
            Span::styled("\"Hello, World!\"", Style::default().fg(Color::Green)),
            Span::raw(")"),
        ]),
        Line::from(vec![
            Span::raw("        "),
            Span::styled("]}", Style::default().fg(Color::Cyan)),
        ]),
        Line::from(vec![
            Span::raw("    "),
            Span::styled("</Paragraph>", Style::default().fg(Color::Blue)),
        ]),
        Line::from(vec![Span::styled(
            "}",
            Style::default()
                .fg(Color::Magenta)
                .add_modifier(Modifier::BOLD),
        )]),
        Line::from(vec![Span::styled(
            "```",
            Style::default()
                .fg(Color::Gray)
                .add_modifier(Modifier::ITALIC),
        )]),
        Line::raw(""),
        Line::from(vec![
            Span::styled(
                "⚠️ Note: ",
                Style::default()
                    .fg(Color::Yellow)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::raw("This is a "),
            Span::styled(
                "demonstration",
                Style::default()
                    .fg(Color::Cyan)
                    .add_modifier(Modifier::ITALIC),
            ),
            Span::raw(" of rich text formatting."),
        ]),
    ];

    rsx! {
        <Block
            title="📚 Rich Documentation Layout"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Cyan)}
        >
            <Paragraph>
                {doc_lines}
            </Paragraph>
        </Block>
    }
}

/// Render text art example
fn render_text_art(state: &AppState) -> VNode {
    let art_lines = vec![
        Line::from(vec![Span::styled(
            "🎨 TEXT ART & DECORATIVE ELEMENTS",
            Style::default()
                .fg(Color::Cyan)
                .add_modifier(Modifier::BOLD | Modifier::UNDERLINED),
        )]),
        Line::raw(""),
        Line::from(vec![Span::styled(
            "╔══════════════════════════════════════╗",
            Style::default().fg(Color::Blue),
        )]),
        Line::from(vec![
            Span::styled("║", Style::default().fg(Color::Blue)),
            Span::styled(
                "    🚀 TUI FRAMEWORK SHOWCASE    ",
                Style::default()
                    .fg(Color::Yellow)
                    .add_modifier(Modifier::BOLD),
            ),
            Span::styled("║", Style::default().fg(Color::Blue)),
        ]),
        Line::from(vec![Span::styled(
            "╚══════════════════════════════════════╝",
            Style::default().fg(Color::Blue),
        )]),
        Line::raw(""),
        Line::from(vec![Span::styled(
            "ASCII Banner:",
            Style::default()
                .fg(Color::White)
                .add_modifier(Modifier::BOLD),
        )]),
        Line::from(vec![Span::styled(
            "████████ ██    ██ ██",
            Style::default().fg(Color::Red),
        )]),
        Line::from(vec![Span::styled(
            "   ██    ██    ██ ██",
            Style::default().fg(Color::Yellow),
        )]),
        Line::from(vec![Span::styled(
            "   ██    ██    ██ ██",
            Style::default().fg(Color::Green),
        )]),
        Line::from(vec![Span::styled(
            "   ██     ██████  ██",
            Style::default().fg(Color::Blue),
        )]),
        Line::raw(""),
        Line::from(vec![Span::styled(
            "Decorative Borders:",
            Style::default()
                .fg(Color::White)
                .add_modifier(Modifier::BOLD),
        )]),
        Line::from(vec![Span::styled(
            "┌─────────────────────────────────────┐",
            Style::default().fg(Color::Cyan),
        )]),
        Line::from(vec![
            Span::styled("│ ", Style::default().fg(Color::Cyan)),
            Span::styled(
                "Elegant box with rounded corners",
                Style::default().fg(Color::White),
            ),
            Span::styled("   │", Style::default().fg(Color::Cyan)),
        ]),
        Line::from(vec![Span::styled(
            "└─────────────────────────────────────┘",
            Style::default().fg(Color::Cyan),
        )]),
        Line::raw(""),
        Line::from(vec![Span::styled(
            "Interactive Elements:",
            Style::default()
                .fg(Color::White)
                .add_modifier(Modifier::BOLD),
        )]),
        Line::from(vec![
            Span::styled(
                "▶ ",
                if state.interactive_state {
                    Style::default().fg(Color::Green)
                } else {
                    Style::default().fg(Color::Gray)
                },
            ),
            Span::styled("Play Button ", Style::default().fg(Color::White)),
            if state.interactive_state {
                Span::styled(
                    "(ACTIVE)",
                    Style::default()
                        .fg(Color::Green)
                        .add_modifier(Modifier::BOLD),
                )
            } else {
                Span::styled("(INACTIVE)", Style::default().fg(Color::Gray))
            },
        ]),
        Line::from(vec![
            Span::styled("♪ ", Style::default().fg(Color::Magenta)),
            Span::styled("Music Note ", Style::default().fg(Color::White)),
            if state.interactive_state {
                Span::styled(
                    "♫ ♪ ♫",
                    Style::default()
                        .fg(Color::Magenta)
                        .add_modifier(Modifier::RAPID_BLINK),
                )
            } else {
                Span::raw("")
            },
        ]),
        Line::raw(""),
        Line::from(vec![Span::styled(
            "Emoji Art:",
            Style::default()
                .fg(Color::White)
                .add_modifier(Modifier::BOLD),
        )]),
        Line::from(vec![Span::styled(
            "🌟 ✨ 🌟 ✨ 🌟 ✨ 🌟 ✨ 🌟",
            Style::default().fg(Color::Yellow),
        )]),
        Line::from(vec![
            Span::styled("🎭 ", Style::default().fg(Color::Red)),
            Span::styled("🎪 ", Style::default().fg(Color::Blue)),
            Span::styled("🎨 ", Style::default().fg(Color::Green)),
            Span::styled("🎯 ", Style::default().fg(Color::Yellow)),
            Span::styled("🎲 ", Style::default().fg(Color::Magenta)),
            Span::styled("🎸 ", Style::default().fg(Color::Cyan)),
            Span::styled("🎺 ", Style::default().fg(Color::Red)),
            Span::styled("🎻 ", Style::default().fg(Color::Blue)),
        ]),
        Line::from(vec![Span::styled(
            "🌈 Rainbow Bridge 🌈",
            Style::default()
                .fg(Color::White)
                .add_modifier(Modifier::BOLD),
        )]),
        Line::raw(""),
        Line::from(vec![Span::styled(
            "Dynamic Pattern:",
            Style::default()
                .fg(Color::White)
                .add_modifier(Modifier::BOLD),
        )]),
        Line::from(vec![
            if state.interactive_state {
                Span::styled(
                    "▓▓▓▓▓▓▓▓▓▓",
                    Style::default()
                        .fg(Color::Red)
                        .add_modifier(Modifier::RAPID_BLINK),
                )
            } else {
                Span::styled("░░░░░░░░░░", Style::default().fg(Color::DarkGray))
            },
            Span::raw(" "),
            if state.interactive_state {
                Span::styled(
                    "▓▓▓▓▓▓▓▓▓▓",
                    Style::default()
                        .fg(Color::Green)
                        .add_modifier(Modifier::RAPID_BLINK),
                )
            } else {
                Span::styled("░░░░░░░░░░", Style::default().fg(Color::DarkGray))
            },
        ]),
    ];

    rsx! {
        <Block
            title="🎨 Creative Text Art & Decorations"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Magenta)}
        >
            <Paragraph>
                {art_lines}
            </Paragraph>
        </Block>
    }
}

/// Main function
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🎨 Starting Rich Text Showcase...");
    println!("🚀 Demonstrating advanced RSX text formatting techniques");

    if let Err(err) = render_async(rich_text_showcase).await {
        eprintln!("❌ Application error: {:?}", err);
    }

    Ok(())
}
