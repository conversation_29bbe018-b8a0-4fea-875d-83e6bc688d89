{"enabled": true, "name": "Code Quality Analyzer", "description": "Monitors source code files for changes and provides comprehensive analysis including code smells, design patterns, best practices, security vulnerabilities, and UX enhancement suggestions", "version": "1", "when": {"type": "userTriggered", "patterns": ["**/*.rs", "**/*.toml", "**/*.js", "**/*.jsx", "**/*.ts", "**/*.tsx", "**/*.py", "**/*.java", "**/*.cpp", "**/*.c", "**/*.h", "**/*.go", "**/*.php", "**/*.rb", "**/*.swift", "**/*.kt"]}, "then": {"type": "askAgent", "prompt": "Analyze the modified code files for potential improvements. Focus on:\n\n1. **Code Quality Analysis:**\n   - Identify code smells and anti-patterns\n   - Suggest better design patterns where applicable\n   - Recommend improvements for readability and maintainability\n   - Point out performance optimization opportunities\n\n2. **Security Vulnerability Scan:**\n   - Check for common security vulnerabilities (SQL injection, XSS, buffer overflows, etc.)\n   - Identify insecure coding practices\n   - Suggest secure alternatives\n\n3. **Best Practices Review:**\n   - Ensure adherence to language-specific best practices\n   - Check for proper error handling\n   - Validate naming conventions and code organization\n\n4. **UX Enhancement Suggestions:**\n   - Recommend advanced features that could improve user experience\n   - Suggest nice-to-have enhancements\n   - Identify opportunities for better user interaction patterns\n\nProvide specific, actionable recommendations while ensuring the existing functionality remains intact. Format your response with clear sections and prioritize suggestions by impact."}}