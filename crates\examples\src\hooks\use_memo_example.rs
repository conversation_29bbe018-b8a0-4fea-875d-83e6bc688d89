//! Professional use_memo Hook Example - Interactive TUI Demo
//!
//! This example demonstrates the use_memo hook with dependency tracking,
//! showing how it optimizes performance by memoizing expensive computations
//! and only recomputing when dependencies change.

use crossterm::event::{Event, KeyCode};
use rink::prelude::*;
use rink_core::hooks::memo::use_memo;
use std::collections::HashMap;
use std::time::Instant;

#[derive(Debu<PERSON>, <PERSON>lone, PartialEq)]
enum DemoMode {
    BasicMemo,
    MultipleDeps,
    ComplexData,
    OnceComputation,
    FieldAccess,
}

#[derive(<PERSON>lone, Debug)]
struct ProcessedData {
    filtered_numbers: Vec<i32>,
    sum: i32,
    average: f64,
    computation_time_ms: u128,
    metadata: HashMap<String, String>,
}

#[derive(Clone, Debug)]
struct ExpensiveConfig {
    settings: HashMap<String, String>,
    computed_hash: String,
    initialization_time: u128,
}

/// Simulate expensive computation
fn expensive_computation(input: i32) -> i32 {
    // Simulate work
    std::thread::sleep(std::time::Duration::from_millis(50));
    input * input * input
}

/// Simulate expensive data processing
fn process_data(numbers: &[i32], threshold: i32) -> ProcessedData {
    let start = Instant::now();
    
    // Simulate expensive filtering and processing
    std::thread::sleep(std::time::Duration::from_millis(30));
    
    let filtered_numbers: Vec<i32> = numbers
        .iter()
        .filter(|&&x| x >= threshold)
        .copied()
        .collect();
    
    let sum: i32 = filtered_numbers.iter().sum();
    let average = if filtered_numbers.is_empty() {
        0.0
    } else {
        sum as f64 / filtered_numbers.len() as f64
    };
    
    let mut metadata = HashMap::new();
    metadata.insert("filter_threshold".to_string(), threshold.to_string());
    metadata.insert("original_count".to_string(), numbers.len().to_string());
    metadata.insert("filtered_count".to_string(), filtered_numbers.len().to_string());
    
    ProcessedData {
        filtered_numbers,
        sum,
        average,
        computation_time_ms: start.elapsed().as_millis(),
        metadata,
    }
}

/// Simulate expensive initialization
fn expensive_initialization() -> ExpensiveConfig {
    let start = Instant::now();
    
    // Simulate expensive initialization
    std::thread::sleep(std::time::Duration::from_millis(100));
    
    let mut settings = HashMap::new();
    settings.insert("theme".to_string(), "dark".to_string());
    settings.insert("language".to_string(), "en".to_string());
    settings.insert("cache_size".to_string(), "1024".to_string());
    
    let computed_hash = format!("hash_{}", start.elapsed().as_nanos());
    
    ExpensiveConfig {
        settings,
        computed_hash,
        initialization_time: start.elapsed().as_millis(),
    }
}

/// Interactive use_memo demonstration app
fn build_ui() -> impl Into<VNode> {
    let (app, controller) = use_app();

    // Application state
    let (input_value, set_input_value) = use_state(5i32);
    let (multiplier, set_multiplier) = use_state(2i32);
    let (threshold, set_threshold) = use_state(3i32);
    let (demo_mode, set_demo_mode) = use_state(DemoMode::BasicMemo);
    let (memo_calls, set_memo_calls) = use_state(0i32);

    // Sample data for processing
    let sample_data = vec![1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

    // Get current values for dependency tracking
    let input_val = input_value.get();
    let multiplier_val = multiplier.get();
    let threshold_val = threshold.get();
    let current_mode = demo_mode.get();

    // 1. Basic memoization - expensive computation
    let basic_memo = use_memo(
        {
            let set_memo_calls = set_memo_calls.clone();
            move || {
                set_memo_calls.update(|c| c + 1);
                expensive_computation(input_val)
            }
        },
        input_val,
    );

    // 2. Multiple dependencies memoization
    let multi_deps_memo = use_memo(
        {
            let set_memo_calls = set_memo_calls.clone();
            move || {
                set_memo_calls.update(|c| c + 1);
                input_val * multiplier_val + (input_val * multiplier_val).pow(2)
            }
        },
        (input_val, multiplier_val),
    );

    // 3. Complex data processing memoization
    let complex_data_memo = use_memo(
        {
            let set_memo_calls = set_memo_calls.clone();
            let sample_data = sample_data.clone();
            move || {
                set_memo_calls.update(|c| c + 1);
                process_data(&sample_data, threshold_val)
            }
        },
        threshold_val,
    );

    // 4. Once computation (expensive initialization)
    let once_memo = use_memo(
        {
            let set_memo_calls = set_memo_calls.clone();
            move || {
                set_memo_calls.update(|c| c + 1);
                expensive_initialization()
            }
        },
        (), // Empty tuple = compute once
    );

    // 5. Field access demonstration
    let field_access_memo = use_memo(
        {
            let set_memo_calls = set_memo_calls.clone();
            move || {
                set_memo_calls.update(|c| c + 1);
                let mut data = HashMap::new();
                data.insert("computed_value".to_string(), (input_val * 100).to_string());
                data.insert("timestamp".to_string(), format!("{}", std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_secs()));
                data.insert("input".to_string(), input_val.to_string());
                data
            }
        },
        input_val,
    );

    // Handle keyboard events
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char('q') | KeyCode::Esc => {
                controller.exit(ExitReason::UserRequested);
            }
            KeyCode::Char('1') => {
                set_demo_mode.set(DemoMode::BasicMemo);
            }
            KeyCode::Char('2') => {
                set_demo_mode.set(DemoMode::MultipleDeps);
            }
            KeyCode::Char('3') => {
                set_demo_mode.set(DemoMode::ComplexData);
            }
            KeyCode::Char('4') => {
                set_demo_mode.set(DemoMode::OnceComputation);
            }
            KeyCode::Char('5') => {
                set_demo_mode.set(DemoMode::FieldAccess);
            }
            KeyCode::Up | KeyCode::Char('+') => {
                set_input_value.update(|current| current + 1);
            }
            KeyCode::Down | KeyCode::Char('-') => {
                set_input_value.update(|current| (current - 1).max(1));
            }
            KeyCode::Left => {
                set_multiplier.update(|current| (current - 1).max(1));
            }
            KeyCode::Right => {
                set_multiplier.update(|current| current + 1);
            }
            KeyCode::Char('t') => {
                set_threshold.update(|current| if *current >= 10 { 1 } else { current + 1 });
            }
            KeyCode::Char('r') => {
                set_input_value.set(5);
                set_multiplier.set(2);
                set_threshold.set(3);
                set_memo_calls.set(0);
            }
            _ => {}
        }
    }

    // Handle app exit
    if app.should_exit() {
        return rsx! {
            <Block
                title="Goodbye!"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Red)}
            >
                <Paragraph
                    style={Style::default().fg(Color::White)}
                    alignment={Alignment::Center}
                >
                    {"Thanks for exploring use_memo!\n\n\
                     Key concepts demonstrated:\n\
                     • Memoization with dependency tracking\n\
                     • Performance optimization through caching\n\
                     • Complex data processing optimization\n\
                     • Once computations for expensive initialization\n\
                     • Efficient field access without full cloning\n\n\
                     The use_memo hook prevents unnecessary\n\
                     recomputations and optimizes performance!"}
                </Paragraph>
            </Block>
        };
    }

    // Get current values for display
    let calls_count = memo_calls.get();
    let metrics = app.metrics();

    // Choose colors based on demo mode
    let border_color = match current_mode {
        DemoMode::BasicMemo => Color::Cyan,
        DemoMode::MultipleDeps => Color::Green,
        DemoMode::ComplexData => Color::Yellow,
        DemoMode::OnceComputation => Color::Magenta,
        DemoMode::FieldAccess => Color::Blue,
    };

    let (mode_description, demo_content) = match current_mode {
        DemoMode::BasicMemo => {
            let result = basic_memo.get().unwrap_or(0);
            (
                "Basic memoization with single dependency",
                format!("Input: {} → Cubed: {}\nMemo Version: {}", input_val, result, basic_memo.version())
            )
        }
        DemoMode::MultipleDeps => {
            let result = multi_deps_memo.get().unwrap_or(0);
            (
                "Memoization with multiple dependencies",
                format!("({} * {}) + ({} * {})² = {}\nMemo Version: {}", 
                    input_val, multiplier_val, input_val, multiplier_val, result, multi_deps_memo.version())
            )
        }
        DemoMode::ComplexData => {
            let data = complex_data_memo.get();
            let content = if let Some(data) = data {
                format!("Filtered: {:?}\nSum: {}, Avg: {:.2}\nComputation: {}ms\nMemo Version: {}", 
                    data.filtered_numbers, data.sum, data.average, data.computation_time_ms, complex_data_memo.version())
            } else {
                "Computing...".to_string()
            };
            ("Complex data processing with memoization", content)
        }
        DemoMode::OnceComputation => {
            let config = once_memo.get();
            let content = if let Some(config) = config {
                format!("Settings: {} items\nHash: {}\nInit Time: {}ms\nMemo Version: {} (should be 1)", 
                    config.settings.len(), config.computed_hash, config.initialization_time, once_memo.version())
            } else {
                "Initializing...".to_string()
            };
            ("Once computation (expensive initialization)", content)
        }
        DemoMode::FieldAccess => {
            let computed_value = field_access_memo.field(|data| data.get("computed_value").cloned()).flatten().unwrap_or_default();
            let timestamp = field_access_memo.field(|data| data.get("timestamp").cloned()).flatten().unwrap_or_default();
            (
                "Efficient field access without full cloning",
                format!("Computed Value: {}\nTimestamp: {}\nMemo Version: {}\n(Accessing fields without cloning entire HashMap)", 
                    computed_value, timestamp, field_access_memo.version())
            )
        }
    };

    rsx! {
        <Block
            title="use_memo Hook Demo"
            borders={Borders::ALL}
            border_style={Style::default().fg(border_color)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Left}
            >
                {format!(
                    "🧠 Professional use_memo Hook Demonstration 🧠\n\n\
                    📊 Current State:\n\
                    Input: {} | Multiplier: {} | Threshold: {}\n\
                    Active Mode: {:?}\n\
                    Description: {}\n\n\
                    📋 Memoized Result:\n\
                    {}\n\n\
                    📈 Performance Metrics:\n\
                    Total Memo Computations: {}\n\
                    App Renders: {} | Events: {}\n\n\
                    🎮 Controls:\n\
                    1-5 : Switch demo modes\n\
                    ↑/+ : Increment input (affects memos)\n\
                    ↓/- : Decrement input (affects memos)\n\
                    ←/→ : Change multiplier (affects multi-deps)\n\
                    t   : Change threshold (affects complex data)\n\
                    r   : Reset all values\n\
                    q   : Quit\n\n\
                    💡 Key Concepts:\n\
                    • Values are memoized based on dependencies\n\
                    • Only recomputed when dependencies change\n\
                    • Optimizes performance for expensive operations\n\
                    • Version tracking shows computation count\n\
                    • Field access avoids unnecessary cloning",
                    input_val,
                    multiplier_val,
                    threshold_val,
                    current_mode,
                    mode_description,
                    demo_content,
                    calls_count,
                    metrics.render_count,
                    metrics.event_count
                )}
            </Paragraph>
        </Block>
    }
}

/// Entry point of the application
fn main() -> Result<(), Box<dyn std::error::Error>> {
    render(build_ui)
}
