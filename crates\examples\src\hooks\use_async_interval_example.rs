//! Comprehensive use_async_interval hook examples
//!
//! This module demonstrates various patterns for the use_async_interval hook,
//! including async data fetching, async operations, and complex async workflows.

use crossterm::event::{Event, KeyCode};
use rink::prelude::*;
use rink_core::hooks::interval::use_async_interval;
use std::time::{Duration, SystemTime, UNIX_EPOCH};

/// Simple async counter example
pub fn simple_async_counter_example() -> impl Into<VNode> {
    let (count, set_count) = use_state(0);
    let (status, set_status) = use_state("Initializing...".to_string());

    // Async interval that simulates async work
    use_async_interval(
        {
            let set_count = set_count.clone();
            let set_status = set_status.clone();
            move || {
                let set_count = set_count.clone();
                let set_status = set_status.clone();
                async move {
                    set_status.set("Processing...".to_string());

                    // Simulate async work
                    tokio::time::sleep(Duration::from_millis(100)).await;

                    set_count.update(|prev| prev + 1);
                    set_status.set("Ready".to_string());
                }
            }
        },
        Duration::from_secs(1),
    );

    rsx! {
        <Block
            title="Simple Async Counter (use_async_interval)"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Green)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Center}
            >
                {format!(
                    "Async Count: {}\nStatus: {}\n\nThis counter increments every second\nwith simulated async processing",
                    count.get(),
                    status.get()
                )}
            </Paragraph>
        </Block>
    }
}

/// Async data fetcher simulation
pub fn async_data_fetcher_example() -> impl Into<VNode> {
    let (data, set_data) = use_state("No data yet".to_string());
    let (fetch_count, set_fetch_count) = use_state(0);
    let (is_loading, set_is_loading) = use_state(false);
    let (last_fetch_time, set_last_fetch_time) = use_state(0u64);

    // Simulate periodic data fetching
    use_async_interval(
        {
            let set_data = set_data.clone();
            let set_fetch_count = set_fetch_count.clone();
            let set_is_loading = set_is_loading.clone();
            let set_last_fetch_time = set_last_fetch_time.clone();
            let fetch_count = fetch_count.clone();
            move || {
                let set_data = set_data.clone();
                let set_fetch_count = set_fetch_count.clone();
                let set_is_loading = set_is_loading.clone();
                let set_last_fetch_time = set_last_fetch_time.clone();
                let fetch_count = fetch_count.clone();
                async move {
                    set_is_loading.set(true);
                    set_data.set("Fetching data...".to_string());

                    // Simulate network delay
                    tokio::time::sleep(Duration::from_millis(500)).await;

                    let timestamp = SystemTime::now()
                        .duration_since(UNIX_EPOCH)
                        .unwrap()
                        .as_secs();

                    // Simulate different types of data
                    let data_types = [
                        "User Profile Data",
                        "System Metrics",
                        "Network Status",
                        "Performance Stats",
                        "Security Logs",
                    ];

                    let fetch_count_val = fetch_count.get() + 1;
                    let data_type = data_types[fetch_count_val % data_types.len()];

                    set_data.set(format!(
                        "{}\nFetched at: {}\nData ID: {}",
                        data_type, timestamp, fetch_count_val
                    ));

                    set_fetch_count.set(fetch_count_val);
                    set_last_fetch_time.set(timestamp);
                    set_is_loading.set(false);
                }
            }
        },
        Duration::from_secs(3), // Fetch every 3 seconds
    );

    let loading_indicator = if is_loading.get() {
        " [Loading...]"
    } else {
        ""
    };

    rsx! {
        <Block
            title={format!("Async Data Fetcher{}", loading_indicator)}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Blue)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Left}
            >
                {format!(
                    "Fetch Count: {}\nLast Fetch: {}\n\nCurrent Data:\n{}\n\nAutomatically fetches new data every 3 seconds",
                    fetch_count.get(),
                    last_fetch_time.get(),
                    data.get()
                )}
            </Paragraph>
        </Block>
    }
}

/// Async health monitor example
pub fn async_health_monitor_example() -> impl Into<VNode> {
    let (services, set_services) = use_state(vec![
        ("Database".to_string(), "Unknown".to_string()),
        ("API Server".to_string(), "Unknown".to_string()),
        ("Cache".to_string(), "Unknown".to_string()),
        ("Queue".to_string(), "Unknown".to_string()),
    ]);
    let (check_count, set_check_count) = use_state(0);
    let (is_checking, set_is_checking) = use_state(false);

    // Periodic health checks
    use_async_interval(
        {
            let set_services = set_services.clone();
            let set_check_count = set_check_count.clone();
            let set_is_checking = set_is_checking.clone();
            move || {
                let set_services = set_services.clone();
                let set_check_count = set_check_count.clone();
                let set_is_checking = set_is_checking.clone();
                async move {
                    set_is_checking.set(true);

                    let mut new_services = Vec::new();

                    // Check each service asynchronously
                    for (service_name, _) in &[
                        ("Database", ""),
                        ("API Server", ""),
                        ("Cache", ""),
                        ("Queue", ""),
                    ] {
                        // Simulate async health check with varying delays
                        let delay = match *service_name {
                            "Database" => 200,
                            "API Server" => 150,
                            "Cache" => 100,
                            "Queue" => 300,
                            _ => 100,
                        };

                        tokio::time::sleep(Duration::from_millis(delay)).await;

                        // Simulate random health status
                        let timestamp = SystemTime::now()
                            .duration_since(UNIX_EPOCH)
                            .unwrap()
                            .as_secs();

                        let is_healthy = (timestamp + service_name.len() as u64) % 4 != 0; // ~75% healthy
                        let status = if is_healthy {
                            format!("✓ Healthy ({}ms)", delay)
                        } else {
                            "✗ Error".to_string()
                        };

                        new_services.push((service_name.to_string(), status));
                    }

                    set_services.set(new_services);
                    set_check_count.update(|prev| prev + 1);
                    set_is_checking.set(false);
                }
            }
        },
        Duration::from_secs(5), // Check every 5 seconds
    );

    let services_list = services.get();
    let services_display = services_list
        .iter()
        .map(|(name, status)| format!("{}: {}", name, status))
        .collect::<Vec<_>>()
        .join("\n");

    let checking_indicator = if is_checking.get() {
        " [Checking...]"
    } else {
        ""
    };

    rsx! {
        <Block
            title={format!("Async Health Monitor{}", checking_indicator)}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Yellow)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Left}
            >
                {format!(
                    "Health Check #{}\n\nService Status:\n{}\n\nChecks run every 5 seconds\nEach service has different response times",
                    check_count.get(),
                    services_display
                )}
            </Paragraph>
        </Block>
    }
}

/// Complex async workflow example
pub fn complex_async_workflow_example() -> impl Into<VNode> {
    let (workflow_state, set_workflow_state) = use_state("Idle".to_string());
    let (step_count, set_step_count) = use_state(0);
    let (results, set_results) = use_state(Vec::<String>::new());
    let (is_active, set_is_active) = use_state(true);

    // Complex multi-step async workflow
    if is_active.get() {
        use_async_interval(
            {
                let set_workflow_state = set_workflow_state.clone();
                let set_step_count = set_step_count.clone();
                let set_results = set_results.clone();
                let step_count = step_count.clone();
                move || {
                    let set_workflow_state = set_workflow_state.clone();
                    let set_step_count = set_step_count.clone();
                    let set_results = set_results.clone();
                    let step_count = step_count.clone();
                    async move {
                        let current_step = step_count.get() + 1;

                        // Step 1: Initialize
                        set_workflow_state.set("Step 1: Initializing...".to_string());
                        tokio::time::sleep(Duration::from_millis(200)).await;

                        // Step 2: Fetch configuration
                        set_workflow_state.set("Step 2: Loading config...".to_string());
                        tokio::time::sleep(Duration::from_millis(300)).await;

                        // Step 3: Process data
                        set_workflow_state.set("Step 3: Processing data...".to_string());
                        tokio::time::sleep(Duration::from_millis(400)).await;

                        // Step 4: Validate results
                        set_workflow_state.set("Step 4: Validating...".to_string());
                        tokio::time::sleep(Duration::from_millis(250)).await;

                        // Step 5: Complete
                        set_workflow_state.set("Step 5: Finalizing...".to_string());
                        tokio::time::sleep(Duration::from_millis(150)).await;

                        let timestamp = SystemTime::now()
                            .duration_since(UNIX_EPOCH)
                            .unwrap()
                            .as_secs();

                        let result =
                            format!("Workflow #{} completed at {}", current_step, timestamp);

                        set_results.update(|prev| {
                            let mut new_vec = prev.clone();
                            new_vec.push(result);
                            // Keep only last 5 results
                            if new_vec.len() > 5 {
                                new_vec.remove(0);
                            }
                            new_vec
                        });

                        set_step_count.set(current_step);
                        set_workflow_state.set("Idle - Waiting for next cycle".to_string());
                    }
                }
            },
            Duration::from_secs(4), // Run workflow every 4 seconds
        );
    }

    // Handle keyboard events
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char(' ') => {
                set_is_active.update(|prev| !prev);
            }
            KeyCode::Char('r') => {
                set_step_count.set(0);
                set_results.set(Vec::new());
                set_workflow_state.set("Reset - Ready to start".to_string());
            }
            _ => {}
        }
    }

    let results_list = results.get();
    let results_display = if results_list.is_empty() {
        "No completed workflows yet".to_string()
    } else {
        results_list.join("\n")
    };

    let status = if is_active.get() { "Active" } else { "Paused" };

    rsx! {
        <Block
            title="Complex Async Workflow (use_async_interval)"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Magenta)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Left}
            >
                {format!(
                    "Status: {}\nCompleted Workflows: {}\n\nCurrent State:\n{}\n\nRecent Results:\n{}\n\nControls:\nSpace : Start/Pause\nr : Reset",
                    status,
                    step_count.get(),
                    workflow_state.get(),
                    results_display
                )}
            </Paragraph>
        </Block>
    }
}

/// Main function to run all use_async_interval examples
pub fn run_use_async_interval_examples() -> Result<(), Box<dyn std::error::Error>> {
    println!("Running use_async_interval examples...");
    println!("Press 'q' to quit any example");
    println!("Note: These examples require a tokio runtime");

    // You can uncomment any of these to run individual examples:
    // render(simple_async_counter_example)?;
    // render(async_data_fetcher_example)?;
    // render(async_health_monitor_example)?;
    render(complex_async_workflow_example)?;

    Ok(())
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    render_async(complex_async_workflow_example).await
}
