//! Comprehensive use_interval hook examples
//!
//! This module demonstrates various patterns for the use_interval hook,
//! including simple timers, dynamic intervals, and state-based intervals.

use crossterm::event::{Event, KeyCode};
use rink::prelude::*;
use rink_core::hooks::interval::use_interval;
use std::time::{Duration, SystemTime, UNIX_EPOCH};

/// Simple timer example with use_interval
pub fn simple_timer_example() -> impl Into<VNode> {
    let (seconds, set_seconds) = use_state(0);
    let (is_running, set_is_running) = use_state(true);

    // Only run interval when timer is active
    if is_running.get() {
        use_interval(
            {
                let set_seconds = set_seconds.clone();
                move || {
                    set_seconds.update(|prev| prev + 1);
                }
            },
            Duration::from_secs(1),
        );
    }

    // Handle keyboard events
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char(' ') => {
                set_is_running.update(|prev| !prev);
            }
            KeyCode::Char('r') => {
                set_seconds.set(0);
            }
            _ => {}
        }
    }

    let status = if is_running.get() {
        "Running"
    } else {
        "Paused"
    };
    let minutes = seconds.get() / 60;
    let secs = seconds.get() % 60;

    rsx! {
        <Block
            title="Simple Timer (use_interval)"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Green)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Center}
            >
                {format!(
                    "Timer: {:02}:{:02}\nStatus: {}\n\nControls:\nSpace : Start/Pause\nr : Reset",
                    minutes,
                    secs,
                    status
                )}
            </Paragraph>
        </Block>
    }
}

/// Counter with different interval speeds
pub fn variable_speed_counter_example() -> impl Into<VNode> {
    let (count, set_count) = use_state(0);
    let (speed_index, set_speed_index) = use_state(0);

    let speeds = [
        ("Slow", Duration::from_secs(2)),
        ("Normal", Duration::from_secs(1)),
        ("Fast", Duration::from_millis(500)),
        ("Very Fast", Duration::from_millis(100)),
    ];

    let (speed_name, interval_duration) = speeds[speed_index.get() % speeds.len()];

    // Interval with dynamic duration
    use_interval(
        {
            let set_count = set_count.clone();
            move || {
                set_count.update(|prev| prev + 1);
            }
        },
        interval_duration,
    );

    // Handle keyboard events
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char('s') => {
                set_speed_index.update(|prev| (prev + 1) % speeds.len());
            }
            KeyCode::Char('r') => {
                set_count.set(0);
            }
            _ => {}
        }
    }

    rsx! {
        <Block
            title="Variable Speed Counter (use_interval)"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Blue)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Center}
            >
                {format!(
                    "Count: {}\nSpeed: {} ({:?})\n\nControls:\ns : Change speed\nr : Reset count",
                    count.get(),
                    speed_name,
                    interval_duration
                )}
            </Paragraph>
        </Block>
    }
}

/// Clock example showing current time
pub fn clock_example() -> impl Into<VNode> {
    let (current_time, set_current_time) = use_state("Loading...".to_string());
    let (tick_count, set_tick_count) = use_state(0);

    // Update clock every second
    use_interval(
        {
            let set_current_time = set_current_time.clone();
            let set_tick_count = set_tick_count.clone();
            move || {
                let now = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs();

                // Simple time formatting (hours:minutes:seconds)
                let hours = (now / 3600) % 24;
                let minutes = (now / 60) % 60;
                let seconds = now % 60;

                set_current_time.set(format!("{:02}:{:02}:{:02} UTC", hours, minutes, seconds));
                set_tick_count.update(|prev| prev + 1);
            }
        },
        Duration::from_secs(1),
    );

    rsx! {
        <Block
            title="Digital Clock (use_interval)"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Cyan)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Center}
            >
                {format!(
                    "Current Time:\n{}\n\nTicks: {}",
                    current_time.get(),
                    tick_count.get()
                )}
            </Paragraph>
        </Block>
    }
}

/// Progress bar example with interval updates
pub fn progress_bar_example() -> impl Into<VNode> {
    let (progress, set_progress) = use_state(0.0);
    let (is_running, set_is_running) = use_state(false);
    let (direction, set_direction) = use_state(1.0); // 1.0 for forward, -1.0 for backward

    // Update progress when running
    if is_running.get() {
        use_interval(
            {
                let set_progress = set_progress.clone();
                let set_direction = set_direction.clone();
                let current_direction = direction.get();
                move || {
                    set_progress.update(|prev| {
                        let new_progress = prev + (current_direction * 2.0); // 2% per tick

                        // Bounce at boundaries
                        if new_progress >= 100.0 {
                            set_direction.set(-1.0);
                            100.0
                        } else if new_progress <= 0.0 {
                            set_direction.set(1.0);
                            0.0
                        } else {
                            new_progress
                        }
                    });
                }
            },
            Duration::from_millis(50), // 20 FPS
        );
    }

    // Handle keyboard events
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char(' ') => {
                set_is_running.update(|prev| !prev);
            }
            KeyCode::Char('r') => {
                set_progress.set(0.0);
                set_direction.set(1.0);
                set_is_running.set(false);
            }
            _ => {}
        }
    }

    let progress_value = progress.get();
    let bar_width = 40;
    let filled_width = ((progress_value / 100.0) * bar_width as f64) as usize;
    let progress_bar = format!(
        "[{}{}] {:.1}%",
        "█".repeat(filled_width),
        "░".repeat(bar_width - filled_width),
        progress_value
    );

    let status = if is_running.get() {
        "Running"
    } else {
        "Stopped"
    };

    rsx! {
        <Block
            title="Animated Progress Bar (use_interval)"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Yellow)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Center}
            >
                {format!(
                    "Progress:\n{}\n\nStatus: {}\nDirection: {}\n\nControls:\nSpace : Start/Stop\nr : Reset",
                    progress_bar,
                    status,
                    if direction.get() > 0.0 { "Forward" } else { "Backward" }
                )}
            </Paragraph>
        </Block>
    }
}

/// Multiple intervals example
pub fn multiple_intervals_example() -> impl Into<VNode> {
    let (fast_counter, set_fast_counter) = use_state(0);
    let (slow_counter, set_slow_counter) = use_state(0);
    let (medium_counter, set_medium_counter) = use_state(0);

    // Fast interval - 10 times per second
    use_interval(
        {
            let set_fast_counter = set_fast_counter.clone();
            move || {
                set_fast_counter.update(|prev| prev + 1);
            }
        },
        Duration::from_millis(100),
    );

    // Medium interval - once per second
    use_interval(
        {
            let set_medium_counter = set_medium_counter.clone();
            move || {
                set_medium_counter.update(|prev| prev + 1);
            }
        },
        Duration::from_secs(1),
    );

    // Slow interval - every 3 seconds
    use_interval(
        {
            let set_slow_counter = set_slow_counter.clone();
            move || {
                set_slow_counter.update(|prev| prev + 1);
            }
        },
        Duration::from_secs(3),
    );

    // Handle keyboard events
    if let Some(Event::Key(key)) = use_event() {
        if let KeyCode::Char('r') = key.code {
            set_fast_counter.set(0);
            set_medium_counter.set(0);
            set_slow_counter.set(0);
        }
    }

    rsx! {
        <Block
            title="Multiple Intervals (use_interval)"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Magenta)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Left}
            >
                {format!(
                    "Fast Counter (100ms): {}\nMedium Counter (1s): {}\nSlow Counter (3s): {}\n\nAll intervals run simultaneously\n\nControls:\nr : Reset all counters",
                    fast_counter.get(),
                    medium_counter.get(),
                    slow_counter.get()
                )}
            </Paragraph>
        </Block>
    }
}

/// Main function to run all use_interval examples
pub fn run_use_interval_examples() -> Result<(), Box<dyn std::error::Error>> {
    println!("Running use_interval examples...");
    println!("Press 'q' to quit any example");

    // You can uncomment any of these to run individual examples:
    // render(simple_timer_example)?;
    // render(variable_speed_counter_example)?;
    // render(clock_example)?;
    // render(progress_bar_example)?;
    render(multiple_intervals_example)?;

    Ok(())
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    run_use_interval_examples()
}
