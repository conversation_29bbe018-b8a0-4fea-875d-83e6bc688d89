//! Comprehensive tests for the use_app hook
//!
//! This module contains extensive tests covering:
//! - Application lifecycle management
//! - State management and data storage
//! - Performance metrics tracking
//! - Configuration management
//! - Thread safety and concurrent access
//! - Integration with other hooks

use super::*;
use crate::hooks::test_utils::{with_component_id, with_test_isolate};
use crossterm::event::{Event, KeyCode, KeyEvent, KeyModifiers};
use parking_lot::Mutex;
use std::time::Duration;

// Global test mutex to ensure app tests run sequentially
static TEST_MUTEX: Mutex<()> = Mutex::new(());

/// Test basic use_app functionality
#[test]
fn test_use_app_basic() {
    let _lock = TEST_MUTEX.lock();
    with_test_isolate(|| {
        super::reset_global_app_container();
        with_component_id("AppBasicComponent", |_context| {
            let (app, controller) = use_app();

            // Initial state should be running (initialized automatically)
            assert_eq!(app.lifecycle(), AppLifecycle::Running);
            assert!(!app.should_exit());
            assert!(app.exit_reason().is_none());

            // Test exit functionality
            controller.exit(ExitReason::UserRequested);
            assert!(app.should_exit());
            assert_eq!(app.exit_reason(), Some(ExitReason::UserRequested));
            assert_eq!(app.lifecycle(), AppLifecycle::Shutting);
        });
    });
}

/// Test application lifecycle transitions
#[test]
fn test_app_lifecycle() {
    let _lock = TEST_MUTEX.lock();
    with_test_isolate(|| {
        super::reset_global_app_container();
        with_component_id("AppLifecycleComponent", |_context| {
            let (app, controller) = use_app();

            // Start in running state
            assert_eq!(app.lifecycle(), AppLifecycle::Running);

            // Test pause
            controller.pause();
            assert_eq!(app.lifecycle(), AppLifecycle::Pausing);

            controller.complete_pause();
            assert_eq!(app.lifecycle(), AppLifecycle::Paused);

            // Test resume
            controller.resume();
            assert_eq!(app.lifecycle(), AppLifecycle::Resuming);

            controller.complete_resume();
            assert_eq!(app.lifecycle(), AppLifecycle::Running);

            // Test shutdown
            controller.shutdown();
            assert_eq!(app.lifecycle(), AppLifecycle::Shutdown);
            assert!(app.should_exit());
            assert_eq!(app.exit_reason(), Some(ExitReason::Completed));
        });
    });
}

/// Test application configuration
#[test]
fn test_app_config() {
    let _lock = TEST_MUTEX.lock();
    with_test_isolate(|| {
        super::reset_global_app_container();
        with_component_id("AppConfigComponent", |_context| {
            let config = AppConfig {
                name: "TestApp".to_string(),
                version: "1.2.3".to_string(),
                debug: true,
                auto_save_interval: Some(Duration::from_secs(30)),
                max_event_queue_size: 500,
                enable_performance_monitoring: true,
            };

            let (app, controller) = use_app_with_config(config.clone());

            // Verify initial configuration
            let app_config = app.config();
            assert_eq!(app_config.name, "TestApp");
            assert_eq!(app_config.version, "1.2.3");
            assert!(app_config.debug);
            assert_eq!(app_config.auto_save_interval, Some(Duration::from_secs(30)));
            assert_eq!(app_config.max_event_queue_size, 500);
            assert!(app_config.enable_performance_monitoring);

            // Test configuration updates
            controller.update_config(|config| {
                config.name = "UpdatedApp".to_string();
                config.debug = false;
            });

            let updated_config = app.config();
            assert_eq!(updated_config.name, "UpdatedApp");
            assert!(!updated_config.debug);
            assert_eq!(updated_config.version, "1.2.3"); // Should remain unchanged
        });
    });
}

/// Test application data storage
#[test]
fn test_app_data_storage() {
    let _lock = TEST_MUTEX.lock();
    with_test_isolate(|| {
        super::reset_global_app_container();
        with_component_id("AppDataComponent", |_context| {
            let (app, controller) = use_app();

            #[derive(Debug, Clone, PartialEq)]
            struct UserData {
                name: String,
                age: u32,
            }

            let user_data = UserData {
                name: "Alice".to_string(),
                age: 30,
            };

            // Store data
            controller.set_data("user", user_data.clone());

            // Retrieve data
            let retrieved_data = app.field(|state| state.get_data::<UserData>("user"));

            assert_eq!(retrieved_data, Some(user_data));

            // Test data doesn't exist
            let missing_data = app.field(|state| state.get_data::<UserData>("missing").is_some());
            assert!(!missing_data);

            // Remove data
            controller.remove_data("user");
            let removed_data = app.field(|state| state.get_data::<UserData>("user").is_some());
            assert!(!removed_data);
        });
    });
}

/// Test performance metrics
#[test]
fn test_app_metrics() {
    let _lock = TEST_MUTEX.lock();
    with_test_isolate(|| {
        super::reset_global_app_container();
        with_component_id("AppMetricsComponent", |_context| {
            let (app, controller) = use_app();

            // Initial metrics
            let initial_metrics = app.metrics();
            assert!(initial_metrics.start_time.is_some());
            assert_eq!(initial_metrics.render_count, 1); // One render from initialization
            assert_eq!(initial_metrics.event_count, 0);

            // Record some events
            let test_event = Event::Key(KeyEvent::new(KeyCode::Char('a'), KeyModifiers::NONE));
            controller.record_event(test_event.clone());
            controller.record_event(test_event);

            let updated_metrics = app.metrics();
            assert_eq!(updated_metrics.event_count, 2);
            assert!(updated_metrics.last_update.is_some());
        });
    });
}

/// Test event processing control
#[test]
fn test_event_processing() {
    let _lock = TEST_MUTEX.lock();
    with_test_isolate(|| {
        super::reset_global_app_container();
        with_component_id("AppEventComponent", |_context| {
            let (app, controller) = use_app();

            // Event processing should be enabled by default
            assert!(app.field(|state| state.event_processing_enabled));

            // Disable event processing
            controller.set_event_processing(false);
            assert!(!app.field(|state| state.event_processing_enabled));

            // Re-enable event processing
            controller.set_event_processing(true);
            assert!(app.field(|state| state.event_processing_enabled));
        });
    });
}

/// Test change detection
#[test]
fn test_app_change_detection() {
    let _lock = TEST_MUTEX.lock();
    with_test_isolate(|| {
        super::reset_global_app_container();
        with_component_id("AppChangeComponent", |_context| {
            let (app, controller) = use_app();

            // Initially no changes
            assert!(!app.has_changed());

            // Make a change
            controller.set_data("test", 42);

            // Should detect change
            assert!(app.has_changed());

            // Access state to update version
            let _ = app.get();

            // Should not detect change after access
            assert!(!app.has_changed());
        });
    });
}

/// Test thread safety
#[test]
fn test_app_thread_safety() {
    let _lock = TEST_MUTEX.lock();
    with_test_isolate(|| {
        super::reset_global_app_container();
        with_component_id("AppThreadComponent", |_context| {
            let (app, controller) = use_app();

            // Simple test: store and retrieve data
            controller.set_data("test_key", 42usize);
            let value = app.field(|state| state.get_data::<usize>("test_key"));
            assert_eq!(value, Some(42));

            // Test multiple data items
            for i in 0..5 {
                controller.set_data(&format!("key_{}", i), i as usize);
            }

            // Verify all data was stored
            for i in 0..5 {
                let value = app.field(|state| state.get_data::<usize>(&format!("key_{}", i)));
                assert_eq!(value, Some(i as usize));
            }
        });
    });
}

/// Test hook persistence across re-renders (like React hooks)
#[test]
fn test_app_hook_persistence() {
    let _lock = TEST_MUTEX.lock();
    with_test_isolate(|| {
        super::reset_global_app_container();

        // Simulate multiple render cycles of the same component
        let mut app_handle = None;
        let mut controller = None;

        // First render
        with_component_id("PersistentComponent", |_context| {
            let (app, ctrl) = use_app();
            ctrl.set_data("persistent_value", 42usize);

            // Store handles for comparison
            app_handle = Some(app);
            controller = Some(ctrl);
        });

        // Second render (same component ID)
        with_component_id("PersistentComponent", |_context| {
            let (app2, _ctrl2) = use_app();

            // Should get the same state, not a new one
            let value = app2.field(|state| state.get_data::<usize>("persistent_value"));
            assert_eq!(value, Some(42));

            // Verify it's the same underlying container (same version)
            assert_eq!(app2.version(), app_handle.as_ref().unwrap().version());
        });
    });
}

/// Test hook behavior with different component instances
#[test]
fn test_app_hook_component_isolation() {
    let _lock = TEST_MUTEX.lock();
    with_test_isolate(|| {
        super::reset_global_app_container();

        // Component A
        with_component_id("ComponentA", |_context| {
            let (_app, controller) = use_app();
            controller.set_data("component_a_data", "from_a".to_string());
        });

        // Component B (different component, but should share global app state)
        with_component_id("ComponentB", |_context| {
            let (app, controller) = use_app();

            // Should see data from Component A (global state)
            let value = app.field(|state| state.get_data::<String>("component_a_data"));
            assert_eq!(value, Some("from_a".to_string()));

            // Add its own data
            controller.set_data("component_b_data", "from_b".to_string());
        });

        // Back to Component A
        with_component_id("ComponentA", |_context| {
            let (app, _controller) = use_app();

            // Should see data from both components
            let value_a = app.field(|state| state.get_data::<String>("component_a_data"));
            let value_b = app.field(|state| state.get_data::<String>("component_b_data"));

            assert_eq!(value_a, Some("from_a".to_string()));
            assert_eq!(value_b, Some("from_b".to_string()));
        });
    });
}

/// Test hook rules compliance (like React hook rules)
#[test]
fn test_app_hook_rules() {
    let _lock = TEST_MUTEX.lock();
    with_test_isolate(|| {
        super::reset_global_app_container();

        with_component_id("HookRulesComponent", |_context| {
            // Rule 1: Hooks should be called in the same order every time
            let (app1, controller1) = use_app();
            let (app2, controller2) = use_app(); // Second call should return same instances

            // Should be the same underlying container
            assert_eq!(app1.version(), app2.version());

            // Changes through one controller should be visible through the other
            controller1.set_data("test", 123usize);
            let value = app2.field(|state| state.get_data::<usize>("test"));
            assert_eq!(value, Some(123));

            // Rule 2: Hook state should be consistent within a render
            controller2.set_data("test", 456usize);
            let value1 = app1.field(|state| state.get_data::<usize>("test"));
            let value2 = app2.field(|state| state.get_data::<usize>("test"));
            assert_eq!(value1, value2);
        });
    });
}

/// Test app hook with custom configuration persistence
#[test]
fn test_app_hook_config_persistence() {
    let _lock = TEST_MUTEX.lock();
    with_test_isolate(|| {
        super::reset_global_app_container();

        let custom_config = AppConfig {
            name: "CustomApp".to_string(),
            version: "2.0.0".to_string(),
            debug: true,
            auto_save_interval: Some(Duration::from_secs(60)),
            max_event_queue_size: 1000,
            enable_performance_monitoring: false,
        };

        // First component with custom config
        with_component_id("ConfigComponent1", |_context| {
            let (app, _controller) = use_app_with_config(custom_config.clone());

            let config = app.config();
            assert_eq!(config.name, "CustomApp");
            assert_eq!(config.version, "2.0.0");
            assert!(config.debug);
        });

        // Second component should get the same config (global container)
        with_component_id("ConfigComponent2", |_context| {
            let (app, _controller) = use_app(); // Default call, but should get existing container

            let config = app.config();
            assert_eq!(config.name, "CustomApp"); // Should persist from first call
            assert_eq!(config.version, "2.0.0");
        });
    });
}

/// Test hook behavior with rapid state changes (like React batching)
#[test]
fn test_app_hook_rapid_updates() {
    let _lock = TEST_MUTEX.lock();
    with_test_isolate(|| {
        super::reset_global_app_container();

        with_component_id("RapidUpdateComponent", |_context| {
            let (app, controller) = use_app();

            // Rapid successive updates
            for i in 0..100 {
                controller.set_data("counter", i);
            }

            // Should have the final value
            let final_value = app.field(|state| state.get_data::<i32>("counter"));
            assert_eq!(final_value, Some(99));

            // Version should have incremented for each update
            assert!(app.version() >= 100); // At least 100 updates + initialization
        });
    });
}

/// Test hook with different data types (type safety)
#[test]
fn test_app_hook_type_safety() {
    let _lock = TEST_MUTEX.lock();
    with_test_isolate(|| {
        super::reset_global_app_container();

        with_component_id("TypeSafetyComponent", |_context| {
            let (app, controller) = use_app();

            // Store different types with same key (should overwrite)
            controller.set_data("multi_type", 42i32);
            controller.set_data("multi_type", "hello".to_string());

            // Should get the string (last stored)
            let string_value = app.field(|state| state.get_data::<String>("multi_type"));
            assert_eq!(string_value, Some("hello".to_string()));

            // Trying to get as i32 should return None (type mismatch)
            let int_value = app.field(|state| state.get_data::<i32>("multi_type"));
            assert_eq!(int_value, None);

            // Store complex types
            #[derive(Debug, Clone, PartialEq)]
            struct ComplexData {
                id: u64,
                name: String,
                active: bool,
            }

            let complex = ComplexData {
                id: 123,
                name: "test".to_string(),
                active: true,
            };

            controller.set_data("complex", complex.clone());
            let retrieved = app.field(|state| state.get_data::<ComplexData>("complex"));
            assert_eq!(retrieved, Some(complex));
        });
    });
}

/// Test hook cleanup and resource management
#[test]
fn test_app_hook_cleanup() {
    let _lock = TEST_MUTEX.lock();
    with_test_isolate(|| {
        super::reset_global_app_container();

        // Test that global state persists across different component instances
        with_component_id("CleanupComponent1", |_context| {
            let (_app, controller) = use_app();
            controller.set_data("persistent_data", 42i32);
        });

        with_component_id("CleanupComponent2", |_context| {
            let (app, controller) = use_app();

            // Should still have data from previous component
            let value = app.field(|state| state.get_data::<i32>("persistent_data"));
            assert_eq!(value, Some(42));

            // Add more data
            controller.set_data("more_data", 100i32);
        });

        // Final verification - all data should still be there
        with_component_id("FinalVerificationComponent", |_context| {
            let (app, _controller) = use_app();

            let value1 = app.field(|state| state.get_data::<i32>("persistent_data"));
            let value2 = app.field(|state| state.get_data::<i32>("more_data"));

            assert_eq!(value1, Some(42));
            assert_eq!(value2, Some(100));
        });
    });
}

/// Test exit interception functionality
#[test]
fn test_app_exit_interception() {
    let _lock = TEST_MUTEX.lock();
    with_test_isolate(|| {
        super::reset_global_app_container();

        with_component_id("ExitInterceptionComponent", |_context| {
            let (app, controller) = use_app();

            // Initially no exit handler
            controller.exit(ExitReason::UserRequested);
            assert!(app.should_exit());

            // Reset for next test
            super::reset_global_app_container();
        });

        with_component_id("ExitInterceptionComponent2", |_context| {
            let (app, controller) = use_app();

            // Set up exit handler that prevents exit
            controller.on_exit_requested(|reason, api| {
                if reason == ExitReason::UserRequested {
                    api.prevent_exit(); // Prevent user-requested exits
                }
                // Allow other exits (default behavior)
            });

            // Try to exit - should be prevented
            controller.exit(ExitReason::UserRequested);
            assert!(!app.should_exit());

            // Try with different reason - should be allowed
            controller.exit(ExitReason::Completed);
            assert!(app.should_exit());
        });
    });
}

/// Test exit interception with conditional logic
#[test]
fn test_app_exit_interception_conditional() {
    let _lock = TEST_MUTEX.lock();
    with_test_isolate(|| {
        super::reset_global_app_container();

        with_component_id("ConditionalExitComponent", |_context| {
            let (app, controller) = use_app();

            // Set up data to control exit behavior
            controller.set_data("allow_exit", false);

            // Set up exit handler that checks app state
            controller.on_exit_requested(|_reason, api| {
                // In a real app, this would check the app state
                // For this test, we'll simulate checking saved state
                api.prevent_exit(); // Prevent exit initially
            });

            // Try to exit - should be prevented
            controller.exit(ExitReason::UserRequested);
            assert!(!app.should_exit());

            // Clear handler and try again
            controller.clear_exit_handler();
            controller.exit(ExitReason::UserRequested);
            assert!(app.should_exit());
        });
    });
}

/// Test exit interception removal
#[test]
fn test_app_exit_interception_removal() {
    let _lock = TEST_MUTEX.lock();
    with_test_isolate(|| {
        super::reset_global_app_container();

        with_component_id("ExitRemovalComponent", |_context| {
            let (app, controller) = use_app();

            // Set up exit handler
            controller.on_exit_requested(|_reason, api| {
                api.prevent_exit(); // Always prevent exit
            });

            // Try to exit - should be prevented
            controller.exit(ExitReason::UserRequested);
            assert!(!app.should_exit());

            // Remove handler
            controller.clear_exit_handler();

            // Try to exit again - should succeed
            controller.exit(ExitReason::UserRequested);
            assert!(app.should_exit());
        });
    });
}

/// Test exit vs shutdown behavior (exit doesn't set global flag, shutdown does)
#[test]
fn test_app_exit_vs_shutdown_behavior() {
    let _lock = TEST_MUTEX.lock();
    with_test_isolate(|| {
        super::reset_global_app_container();

        with_component_id("ExitShutdownComponent", |_context| {
            let (app, controller) = use_app();

            // Initially not exiting
            assert!(!app.should_exit());
            assert!(!super::should_exit_globally());

            // Call exit() - should set internal state but NOT global flag
            controller.exit(ExitReason::UserRequested);

            // App should report it should exit, but global flag should still be false
            assert!(app.should_exit());
            assert!(!super::should_exit_globally()); // This is the key test!
            assert_eq!(app.lifecycle(), AppLifecycle::Shutting);
            assert_eq!(app.exit_reason(), Some(ExitReason::UserRequested));

            // Now call shutdown() - should set global flag
            controller.shutdown();

            // Now global flag should be set
            assert!(app.should_exit());
            assert!(super::should_exit_globally());
            assert_eq!(app.lifecycle(), AppLifecycle::Shutdown);
        });
    });
}

/// Test exit interception with delayed shutdown
#[test]
fn test_app_exit_interception_with_delayed_shutdown() {
    let _lock = TEST_MUTEX.lock();
    with_test_isolate(|| {
        super::reset_global_app_container();

        with_component_id("DelayedShutdownComponent", |_context| {
            let (app, controller) = use_app();

            // Set up exit handler that allows exit
            controller.on_exit_requested(|_reason, _api| {
                // Allow exit (default behavior)
            });

            // Call exit() - should set internal state but not global flag
            controller.exit(ExitReason::UserRequested);

            // App should be in shutting state but renderer should continue
            assert!(app.should_exit());
            assert!(!super::should_exit_globally());
            assert_eq!(app.lifecycle(), AppLifecycle::Shutting);

            // Simulate countdown delay (app can render shutdown UI here)
            std::thread::sleep(Duration::from_millis(10));

            // App should still be in shutting state, global flag still false
            assert!(app.should_exit());
            assert!(!super::should_exit_globally());

            // Now call shutdown to complete the exit
            controller.shutdown();

            // Now global flag should be set
            assert!(super::should_exit_globally());
            assert_eq!(app.lifecycle(), AppLifecycle::Shutdown);
        });
    });
}

/// Test exit interception prevents global flag from being set
#[test]
fn test_app_exit_interception_prevents_global_flag() {
    let _lock = TEST_MUTEX.lock();
    with_test_isolate(|| {
        super::reset_global_app_container();

        with_component_id("PreventGlobalFlagComponent", |_context| {
            let (app, controller) = use_app();

            // Set up exit handler that prevents exit
            controller.on_exit_requested(|_reason, api| {
                api.prevent_exit();
            });

            // Try to exit - should be intercepted
            controller.exit(ExitReason::UserRequested);

            // Neither internal nor global exit should be set
            assert!(!app.should_exit());
            assert!(!super::should_exit_globally());
            assert_eq!(app.lifecycle(), AppLifecycle::Running);

            // Clear handler and try again
            controller.clear_exit_handler();
            controller.exit(ExitReason::UserRequested);

            // Now internal exit should be set but not global
            assert!(app.should_exit());
            assert!(!super::should_exit_globally());
            assert_eq!(app.lifecycle(), AppLifecycle::Shutting);
        });
    });
}

/// Test shutdown without prior exit call
#[test]
fn test_app_direct_shutdown() {
    let _lock = TEST_MUTEX.lock();
    with_test_isolate(|| {
        super::reset_global_app_container();

        with_component_id("DirectShutdownComponent", |_context| {
            let (app, controller) = use_app();

            // Initially not exiting
            assert!(!app.should_exit());
            assert!(!super::should_exit_globally());

            // Call shutdown directly (without exit)
            controller.shutdown();

            // Both internal and global flags should be set
            assert!(app.should_exit());
            assert!(super::should_exit_globally());
            assert_eq!(app.lifecycle(), AppLifecycle::Shutdown);
            assert_eq!(app.exit_reason(), Some(ExitReason::Completed)); // Default reason
        });
    });
}

/// Test exit interception with different exit reasons
#[test]
fn test_app_exit_interception_by_reason() {
    let _lock = TEST_MUTEX.lock();
    with_test_isolate(|| {
        super::reset_global_app_container();

        with_component_id("ExitReasonComponent", |_context| {
            let (app, controller) = use_app();

            // Set up exit handler that only prevents user-requested exits
            controller.on_exit_requested(|reason, api| {
                match reason {
                    ExitReason::UserRequested => api.prevent_exit(), // Prevent user exits
                    _ => {
                        // Allow other exits
                    }
                }
            });

            // Try user-requested exit - should be prevented
            controller.exit(ExitReason::UserRequested);
            assert!(!app.should_exit());
            assert!(!super::should_exit_globally());

            // Try error exit - should be allowed
            controller.exit(ExitReason::Error("test error".to_string()));
            assert!(app.should_exit());
            assert!(!super::should_exit_globally()); // Still no global flag until shutdown
            assert_eq!(app.lifecycle(), AppLifecycle::Shutting);

            // Complete the shutdown
            controller.shutdown();
            assert!(super::should_exit_globally());
        });
    });
}

/// Test complete exit flow with countdown simulation (like simple_demo)
#[test]
fn test_app_complete_exit_flow_with_countdown() {
    let _lock = TEST_MUTEX.lock();
    with_test_isolate(|| {
        super::reset_global_app_container();

        with_component_id("CountdownExitComponent", |_context| {
            let (app, controller) = use_app();

            // Set up exit handler that allows exit
            controller.on_exit_requested(|reason, _api| if reason == ExitReason::UserRequested {});

            // Step 1: User requests exit
            controller.exit(ExitReason::UserRequested);

            // Step 2: App should be in shutting state, ready to show countdown UI
            assert!(app.should_exit());
            assert!(!super::should_exit_globally()); // Renderer continues running
            assert_eq!(app.lifecycle(), AppLifecycle::Shutting);
            assert_eq!(app.exit_reason(), Some(ExitReason::UserRequested));

            // Step 3: Simulate countdown period (app can render "Shutting Down..." UI)
            for _ in 0..5 {
                // During countdown, app should still be in shutting state
                assert!(app.should_exit());
                assert!(!super::should_exit_globally());
                assert_eq!(app.lifecycle(), AppLifecycle::Shutting);

                // Simulate frame rendering during countdown
                std::thread::sleep(Duration::from_millis(1));
            }

            // Step 4: Countdown completes, call shutdown
            controller.shutdown();

            // Step 5: Now global flag should be set and renderer should exit
            assert!(app.should_exit());
            assert!(super::should_exit_globally());
            assert_eq!(app.lifecycle(), AppLifecycle::Shutdown);
        });
    });
}

/// Test exit interception with confirmation dialog simulation
#[test]
fn test_app_exit_confirmation_dialog_flow() {
    let _lock = TEST_MUTEX.lock();
    with_test_isolate(|| {
        super::reset_global_app_container();

        with_component_id("ConfirmationDialogComponent", |_context| {
            let (app, controller) = use_app();

            // Set up exit handler that shows confirmation dialog
            controller.on_exit_requested(|_reason, api| {
                api.prevent_exit(); // Prevent immediate exit, show confirmation first
            });

            // Step 1: User presses 'q' - should show confirmation dialog
            controller.exit(ExitReason::UserRequested);

            // Exit should be prevented, app continues running
            assert!(!app.should_exit());
            assert!(!super::should_exit_globally());
            assert_eq!(app.lifecycle(), AppLifecycle::Running);

            // Step 2: User confirms exit by pressing 'y'
            // Clear handler and exit again
            controller.clear_exit_handler();
            controller.exit(ExitReason::UserRequested);

            // Now exit should proceed to shutting state
            assert!(app.should_exit());
            assert!(!super::should_exit_globally()); // Still no global flag
            assert_eq!(app.lifecycle(), AppLifecycle::Shutting);

            // Step 3: Complete shutdown after countdown
            controller.shutdown();

            // Final state: global flag set
            assert!(super::should_exit_globally());
            assert_eq!(app.lifecycle(), AppLifecycle::Shutdown);
        });
    });
}

/// Test exit interception with cancellation
#[test]
fn test_app_exit_cancellation_flow() {
    let _lock = TEST_MUTEX.lock();
    with_test_isolate(|| {
        super::reset_global_app_container();

        with_component_id("CancellationComponent", |_context| {
            let (app, controller) = use_app();

            // Set up exit handler that prevents exit
            controller.on_exit_requested(|_reason, api| {
                api.prevent_exit();
            });

            // Try to exit multiple times - should always be prevented
            for _ in 0..3 {
                controller.exit(ExitReason::UserRequested);

                // Should remain in running state
                assert!(!app.should_exit());
                assert!(!super::should_exit_globally());
                assert_eq!(app.lifecycle(), AppLifecycle::Running);
            }

            // Clear handler and try again - should work
            controller.clear_exit_handler();
            controller.exit(ExitReason::UserRequested);

            // Now should proceed to shutting state
            assert!(app.should_exit());
            assert!(!super::should_exit_globally());
            assert_eq!(app.lifecycle(), AppLifecycle::Shutting);
        });
    });
}

/// Test new Tauri-style exit handler API
#[test]
fn test_app_tauri_style_exit_handler() {
    let _lock = TEST_MUTEX.lock();
    with_test_isolate(|| {
        super::reset_global_app_container();

        with_component_id("TauriStyleExitComponent", |_context| {
            let (app, controller) = use_app();

            // Test explicit prevent_exit()
            controller.on_exit_requested(|reason, api| {
                if reason == ExitReason::UserRequested {
                    api.prevent_exit()
                }
            });

            // Try user-requested exit - should be prevented
            controller.exit(ExitReason::UserRequested);
            assert!(!app.should_exit());
            assert!(!super::should_exit_globally());

            // Try error exit - should be allowed
            controller.exit(ExitReason::Error("test".to_string()));
            assert!(app.should_exit());
            assert!(!super::should_exit_globally()); // Still no global flag until shutdown

            // Reset for next test
            super::reset_global_app_container();
        });

        with_component_id("TauriStyleAllowComponent", |_context| {
            let (app, controller) = use_app();

            // Test explicit allow_exit()
            controller.on_exit_requested(|_reason, api| {
                api.allow_exit(); // Explicitly allow (though this is default)
            });

            // Exit should be allowed
            controller.exit(ExitReason::UserRequested);
            assert!(app.should_exit());
            assert!(!super::should_exit_globally());
        });
    });
}

/// Test multiple app instances (should share state)
#[test]
fn test_multiple_app_instances() {
    let _lock = TEST_MUTEX.lock();
    with_test_isolate(|| {
        super::reset_global_app_container();
        with_component_id("AppMultipleComponent", |_context| {
            let (app1, controller1) = use_app();
            let (app2, controller2) = use_app();

            // Both should reference the same state
            controller1.set_data("shared", "value".to_string());

            let value1 = app1.field(|state| state.get_data::<String>("shared"));
            let value2 = app2.field(|state| state.get_data::<String>("shared"));

            assert_eq!(value1, Some("value".to_string()));
            assert_eq!(value2, Some("value".to_string()));

            // Exit from one should affect both
            controller2.exit(ExitReason::Completed);
            assert!(app1.should_exit());
            assert!(app2.should_exit());
        });
    });
}
