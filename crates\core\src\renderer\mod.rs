use crossterm::{
    event::{self, DisableMouseCapture, EnableMouseCapture},
    execute,
    terminal::{EnterAlternateScreen, LeaveAlternateScreen, disable_raw_mode, enable_raw_mode},
};
use ratatui::{
    Terminal,
    backend::CrosstermBackend,
    buffer::Buffer,
    layout::{Constraint, Direction, Layout, Rect},
    widgets::Widget,
};
use std::{
    any::{Any, TypeId},
    io,
    rc::Rc,
    sync::Arc,
};

use crate::hooks::{
    HookContext,
    app::{set_global_exit_flag, should_exit_globally},
    clear_hook_context,
    event::set_current_event_context,
    set_hook_context,
};

/// Type alias for the render function
type RenderFn = Arc<dyn Fn(&dyn Any, Rect, &mut Buffer) + Send + Sync>;

/// Trait for components in the virtual DOM system
pub trait Component: Send + Sync {
    /// Render the component
    fn render(&self, props: &dyn Any) -> VNode;
}

/// Block component that can contain children
#[derive(Clone)]
pub struct BlockWithChildren {
    pub block: ratatui::widgets::Block<'static>,
    pub children: Vec<VNode>,
}

impl BlockWithChildren {
    pub fn new(block: ratatui::widgets::Block<'static>, children: Vec<VNode>) -> Self {
        Self { block, children }
    }
}

/// Layout widget that wraps native Ratatui Layout for proper IDE integration
#[derive(Clone)]
pub struct LayoutWidget {
    pub layout: ratatui::layout::Layout,
    pub children: Vec<VNode>,
}

impl LayoutWidget {
    pub fn new(layout: ratatui::layout::Layout, children: Vec<VNode>) -> Self {
        Self { layout, children }
    }
}

/// Represents an element in the virtual DOM (placeholder for now)
#[derive(Clone)]
pub struct Element {
    pub tag: String,
    pub props: Arc<dyn Any + Send + Sync>,
    pub children: Vec<VNode>,
}

/// Represents a virtual node in the virtual DOM tree.
#[derive(Clone)]
pub enum VNode {
    /// Represents a component in the virtual DOM tree.
    Component {
        /// The type ID of the component.
        type_id: TypeId,
        /// The props of the component.
        props: Arc<dyn Any + Send + Sync>,
        /// The children of the component.
        children: Vec<Element>,
        /// The key of the component.
        key: Option<String>,
        /// The actual component instance.
        component: Arc<dyn Component + Send + Sync>,
    },
    /// Represents a primitive widget in the virtual DOM tree.
    Widget {
        /// The widget instance.
        widget: Arc<dyn Any + Send + Sync>,
        /// Render function that knows how to render this specific widget
        render_fn: RenderFn,
        /// The key of the widget.
        key: Option<String>,
    },
    /// Represents a layout container that arranges children.
    Layout {
        /// The layout configuration.
        layout: Layout,
        /// The children to be arranged by the layout.
        children: Vec<VNode>,
    },
    /// Represents a text node in the virtual DOM tree.
    Text(String),
}

impl VNode {
    /// Create a VNode from a widget
    pub fn widget<W>(widget: W) -> Self
    where
        W: Into<VNode>,
    {
        widget.into()
    }

    /// Create a VNode from text content
    pub fn text<T>(text: T) -> Self
    where
        T: ToString,
    {
        VNode::Text(text.to_string())
    }

    /// Create a VNode from a component (for now, treat as text)
    pub fn component<C>(component: C) -> Self
    where
        C: ToString,
    {
        VNode::Text(component.to_string())
    }
}

impl ratatui::widgets::Widget for VNode {
    fn render(self, area: ratatui::layout::Rect, buf: &mut ratatui::buffer::Buffer) {
        match self {
            VNode::Component {
                component, props, ..
            } => {
                // Render the component by calling its render method
                let rendered = component.render(props.as_ref());
                rendered.render(area, buf);
            }
            VNode::Widget {
                widget, render_fn, ..
            } => {
                // Use the render function to render the widget
                render_fn(widget.as_ref(), area, buf);
            }
            VNode::Layout { layout, children } => {
                // Split the area according to the layout and render children
                let chunks = layout.split(area);
                for (child, chunk) in children.into_iter().zip(chunks.iter()) {
                    child.render(*chunk, buf);
                }
            }
            VNode::Text(text) => {
                // Render text as a paragraph
                let paragraph = ratatui::widgets::Paragraph::new(text);
                paragraph.render(area, buf);
            }
        }
    }
}

impl std::fmt::Display for VNode {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            VNode::Component { .. } => write!(f, "Component"),
            VNode::Widget { .. } => write!(f, "Widget"),
            VNode::Layout { children, .. } => write!(f, "Layout({})", children.len()),
            VNode::Text(text) => write!(f, "{}", text),
        }
    }
}

// Helper functions to create render functions for different widget types
pub fn create_block_render_fn() -> RenderFn {
    Arc::new(|widget: &dyn Any, area: Rect, buf: &mut Buffer| {
        if let Some(block) = widget.downcast_ref::<ratatui::widgets::Block>() {
            block.clone().render(area, buf);
        }
    })
}

pub fn create_paragraph_render_fn() -> RenderFn {
    Arc::new(|widget: &dyn Any, area: Rect, buf: &mut Buffer| {
        if let Some(paragraph) = widget.downcast_ref::<ratatui::widgets::Paragraph>() {
            paragraph.clone().render(area, buf);
        }
    })
}

pub fn create_gauge_render_fn() -> RenderFn {
    Arc::new(|widget: &dyn Any, area: Rect, buf: &mut Buffer| {
        if let Some(gauge) = widget.downcast_ref::<ratatui::widgets::Gauge>() {
            gauge.clone().render(area, buf);
        }
    })
}

pub fn create_list_render_fn() -> RenderFn {
    Arc::new(|widget: &dyn Any, area: Rect, buf: &mut Buffer| {
        if let Some(list) = widget.downcast_ref::<ratatui::widgets::List>() {
            list.clone().render(area, buf);
        }
    })
}

// Implement From<T> for VNode for common Ratatui widgets (gives Into<VNode> for free)
impl From<ratatui::widgets::Block<'static>> for VNode {
    fn from(widget: ratatui::widgets::Block<'static>) -> Self {
        VNode::Widget {
            widget: Arc::new(widget),
            render_fn: create_block_render_fn(),
            key: None,
        }
    }
}

impl From<ratatui::widgets::Paragraph<'static>> for VNode {
    fn from(widget: ratatui::widgets::Paragraph<'static>) -> Self {
        VNode::Widget {
            widget: Arc::new(widget),
            render_fn: create_paragraph_render_fn(),
            key: None,
        }
    }
}

impl From<ratatui::widgets::Gauge<'static>> for VNode {
    fn from(widget: ratatui::widgets::Gauge<'static>) -> Self {
        VNode::Widget {
            widget: Arc::new(widget),
            render_fn: create_gauge_render_fn(),
            key: None,
        }
    }
}

impl From<ratatui::widgets::List<'static>> for VNode {
    fn from(widget: ratatui::widgets::List<'static>) -> Self {
        VNode::Widget {
            widget: Arc::new(widget),
            render_fn: create_list_render_fn(),
            key: None,
        }
    }
}

impl From<String> for VNode {
    fn from(text: String) -> Self {
        VNode::Text(text)
    }
}

impl From<&str> for VNode {
    fn from(text: &str) -> Self {
        VNode::Text(text.to_string())
    }
}

pub fn create_block_with_children_render_fn() -> RenderFn {
    Arc::new(|widget: &dyn Any, area: Rect, buf: &mut Buffer| {
        if let Some(block_with_children) = widget.downcast_ref::<BlockWithChildren>() {
            // First render the block border
            block_with_children.block.clone().render(area, buf);

            // Then render children in the inner area
            let inner_area = block_with_children.block.inner(area);

            // If there's only one child, render it directly
            if block_with_children.children.len() == 1 {
                block_with_children.children[0]
                    .clone()
                    .render(inner_area, buf);
            } else if !block_with_children.children.is_empty() {
                // Multiple children - create a vertical layout
                let layout = Layout::default()
                    .direction(Direction::Vertical)
                    .constraints(vec![
                        Constraint::Percentage(
                            100 / block_with_children.children.len() as u16
                        );
                        block_with_children.children.len()
                    ]);
                let chunks = layout.split(inner_area);

                for (child, chunk) in block_with_children.children.iter().zip(chunks.iter()) {
                    child.clone().render(*chunk, buf);
                }
            }
        }
    })
}

pub fn create_layout_render_fn() -> RenderFn {
    Arc::new(|widget: &dyn Any, area: Rect, buf: &mut Buffer| {
        if let Some(layout_widget) = widget.downcast_ref::<LayoutWidget>() {
            // Use the native ratatui layout directly
            let chunks = layout_widget.layout.split(area);

            // Render each child in its corresponding chunk
            for (child, chunk) in layout_widget.children.iter().zip(chunks.iter()) {
                child.clone().render(*chunk, buf);
            }
        }
    })
}

/// Render a widget to the terminal with a simple event loop
pub fn render<F, R>(initializer: F) -> Result<(), Box<dyn std::error::Error>>
where
    F: Fn() -> R,
    R: Into<VNode>,
{
    // Setup terminal
    enable_raw_mode()?;
    let mut stdout = io::stdout();
    execute!(stdout, EnterAlternateScreen, EnableMouseCapture)?;
    let backend = CrosstermBackend::new(stdout);
    let mut terminal = Terminal::new(backend)?;

    // Create hook context
    let hook_context = Rc::new(HookContext::new());

    // Set the hook context for the renderer
    // This allows components to access hooks like use_state, use_effect, etc.
    set_hook_context(hook_context.clone());

    // Reset global exit flag at start
    set_global_exit_flag(false);

    // Main loop
    loop {
        // Check if application wants to exit
        if should_exit_globally() {
            break;
        }

        // Handle events first
        if event::poll(std::time::Duration::from_millis(16))? {
            let event = event::read()?;
            set_current_event_context(Some(Arc::new(event)));
        } else {
            // Clear event context if no event
            set_current_event_context(None);
        }

        // Reset hooks for new render cycle
        hook_context.reset();

        terminal.draw(|f| {
            let widget: R = initializer();
            f.render_widget(widget.into(), f.area());
        })?;
    }

    // Clear the event context after rendering
    set_current_event_context(None);
    // Cleanup
    clear_hook_context();

    // Restore terminal
    disable_raw_mode()?;
    execute!(
        terminal.backend_mut(),
        LeaveAlternateScreen,
        DisableMouseCapture
    )?;
    terminal.show_cursor()?;

    Ok(())
}

/// Async version of render function that supports tokio runtime and async components
///
/// This function provides the same functionality as `render` but runs within a tokio
/// async context, allowing for proper integration with async hooks like `use_async_effect`
/// and `use_async_interval`.
///
/// # Features
/// - Full async/await support for component rendering
/// - Integration with tokio runtime for async hooks
/// - Proper async cleanup and resource management
/// - Non-blocking event handling with async intervals
/// - Support for async state updates and effects
///
/// # Requirements
/// - Must be called within a tokio runtime context
/// - Components can use both sync and async hooks
/// - Async hooks like `use_async_effect` and `use_async_interval` work properly
///
/// # Example
/// ```rust,no_run
/// use rink_core::{render_async, VNode};
///
/// #[tokio::main]
/// async fn main() -> Result<(), Box<dyn std::error::Error>> {
///     render_async(|| {
///         // Your async-enabled component here
///         VNode::Text("Hello Async World!".to_string())
///     }).await
/// }
/// ```
pub async fn render_async<F, R>(initializer: F) -> Result<(), Box<dyn std::error::Error>>
where
    F: Fn() -> R,
    R: Into<VNode>,
{
    // Setup terminal
    enable_raw_mode()?;
    let mut stdout = io::stdout();
    execute!(stdout, EnterAlternateScreen, EnableMouseCapture)?;
    let backend = CrosstermBackend::new(stdout);
    let mut terminal = Terminal::new(backend)?;

    // Create hook context
    let hook_context = Rc::new(HookContext::new());

    // Set the hook context for the renderer
    set_hook_context(hook_context.clone());

    // Reset global exit flag at start
    set_global_exit_flag(false);

    // Main async loop - run directly without spawning to avoid Send issues
    let mut event_interval = tokio::time::interval(std::time::Duration::from_millis(16));

    loop {
        tokio::select! {
            _ = event_interval.tick() => {
                // Check if application wants to exit
                if should_exit_globally() {
                    break;
                }

                // Reset hooks for new render cycle
                hook_context.reset();

                // Draw the UI
                if let Err(e) = terminal.draw(|f| {
                    let widget: R = initializer();
                    f.render_widget(widget.into(), f.area());
                }) {
                    eprintln!("Render error: {}", e);
                    break;
                }

                // Handle events non-blocking
                if let Ok(true) = event::poll(std::time::Duration::from_millis(0)) {
                    match event::read() {
                        Ok(event) => {
                            set_current_event_context(Some(Arc::new(event)));
                        }
                        Err(e) => {
                            eprintln!("Event read error: {}", e);
                        }
                    }
                }
            }
        }
    }

    // Clear the event context after rendering
    set_current_event_context(None);

    // Cleanup
    clear_hook_context();

    // Restore terminal
    disable_raw_mode()?;
    execute!(
        terminal.backend_mut(),
        LeaveAlternateScreen,
        DisableMouseCapture
    )?;
    terminal.show_cursor()?;

    Ok(())
}
