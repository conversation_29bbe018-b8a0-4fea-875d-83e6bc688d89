[package]
name = "rink_core"
version = "0.1.0"
edition = "2024"

[features]
default = []
file-persistence = []

[dependencies]
battery = "0.7"
better-panic = { workspace = true }
crossbeam-channel = { workspace = true }
crossterm = { workspace = true }
dashmap = { workspace = true }
human-panic = { workspace = true }
once_cell = { workspace = true }
parking_lot = { workspace = true }
ratatui = { workspace = true, features = ["all-widgets"] }
reqwest = { workspace = true, features = ["json"] }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
tokio = { workspace = true }
tracing = { workspace = true }
tracing-appender = { workspace = true }
tracing-subscriber = { workspace = true, features = ["env-filter", "json"] }
