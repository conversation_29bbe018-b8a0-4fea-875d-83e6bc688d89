pub use rink_core::hooks::app::{
    App<PERSON><PERSON>fig, A<PERSON><PERSON><PERSON><PERSON>er, App<PERSON><PERSON><PERSON>, AppLifecycle, AppMetrics, ExitReason, use_app,
    use_app_with_config,
};
pub use rink_core::hooks::effect::use_effect;
pub use rink_core::hooks::event::{set_current_event_context, use_event};
pub use rink_core::hooks::interval::{use_async_interval, use_interval};
pub use rink_core::hooks::state::{StateHandle, StateSetter, use_state};
pub use rink_core::{VNode, render, render_async};
pub use rink_core_macros::rsx;

/// Prelude module for common imports
pub mod prelude {
    pub use crate::*;

    // Re-export commonly used Ratatui types for convenience
    pub use ratatui::{
        layout::{Alignment, Layout},
        style::{Color, Style},
        widgets::*,
    };
}
