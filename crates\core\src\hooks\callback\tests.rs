use super::*;
use crate::hooks::{HookContext, set_hook_context};
use std::rc::Rc;
use std::sync::{Arc, Mutex};

#[test]
fn test_use_callback_basic() {
    let context = Rc::new(HookContext::new());
    set_hook_context(context.clone());

    let result = Arc::new(Mutex::new(0));
    let result_clone = result.clone();

    // First call - should create new callback
    let callback1 = use_callback(
        CallbackFactory(move || {
            let result = result_clone.clone();
            Callback::from(move |x: i32| {
                *result.lock().unwrap() = x;
            })
        }),
        42,
    );

    callback1.emit(100);
    assert_eq!(*result.lock().unwrap(), 100);

    // Reset context for next render
    context.reset();

    // Second call with same deps - should reuse callback
    let result_clone2 = result.clone();
    let callback2 = use_callback(
        CallbackFactory(move || {
            let result = result_clone2.clone();
            Callback::from(move |x: i32| {
                *result.lock().unwrap() = x * 2; // Different behavior
            })
        }),
        42, // Same deps
    );

    callback2.emit(50);
    // Should still use old callback behavior
    assert_eq!(*result.lock().unwrap(), 50);
}

#[test]
fn test_use_callback_deps_change() {
    let context = Rc::new(HookContext::new());
    set_hook_context(context.clone());

    let result = Arc::new(Mutex::new(0));
    let result_clone = result.clone();

    // First call
    let _callback1 = use_callback(
        CallbackFactory(move || {
            let result = result_clone.clone();
            Callback::from(move |x: i32| {
                *result.lock().unwrap() = x;
            })
        }),
        42,
    );

    context.reset();

    let result_clone2 = result.clone();
    // Second call with different deps - should create new callback
    let callback2 = use_callback(
        CallbackFactory(move || {
            let result = result_clone2.clone();
            Callback::from(move |x: i32| {
                *result.lock().unwrap() = x * 3; // Different behavior
            })
        }),
        43, // Different deps
    );

    callback2.emit(10);
    // Should use new callback behavior
    assert_eq!(*result.lock().unwrap(), 30);
}

#[test]
fn test_use_callback_once() {
    let context = Rc::new(HookContext::new());
    set_hook_context(context.clone());

    let result = Arc::new(Mutex::new(String::new()));
    let result_clone = result.clone();

    let callback = use_callback_once(CallbackFactory(move || {
        let result = result_clone.clone();
        Callback::from(move |msg: String| {
            *result.lock().unwrap() = msg;
        })
    }));

    callback.emit("Hello".to_string());
    assert_eq!(*result.lock().unwrap(), "Hello");
}

#[test]
fn test_use_event_handler() {
    let context = Rc::new(HookContext::new());
    set_hook_context(context.clone());

    let result = Arc::new(Mutex::new(0));
    let result_clone = result.clone();

    let handler = use_event_handler(
        move |x: i32| {
            *result_clone.lock().unwrap() = x * 2;
        },
        42,
    );

    handler.emit(5);
    assert_eq!(*result.lock().unwrap(), 10);
}
